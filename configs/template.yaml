# Configuration template showing all possible parameters
# Copy this template to create environment-specific config files:
# - configs/local.yaml (for local development)
# - configs/development.yaml (for dev environment) 
# - configs/production.yaml (for production environment)

claude:
  # Direct API key for Claude API
  # Used only in local environment - stores the actual API key value
  # For security, this should only be used in local development
  # Example: "sk-ant-api03-..."
  api_key: "your-claude-api-key-here"
  
  # AWS Secrets Manager secret name
  # Used in development and production environments
  # Specifies the name of the secret in AWS Secrets Manager that contains the Claude API key
  # If not specified, defaults to "claude-api-key"
  # The secret should contain the API key as a plain string value
  secret_name: "claude-api-key"

database:
  # MongoDB connection configuration
  uri: "mongodb://localhost:27017"
  database: "impact-resume-service"

# Environment-specific behavior:
# - local: Uses environment variables for auth secrets, api_key directly from this file
# - development/production: Uses AWS Secrets Manager with secret_name, ignores api_key