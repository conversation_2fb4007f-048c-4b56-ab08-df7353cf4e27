# CLAUDE.md

This file provides guidance to AI coding agents when working with code in this repository.

## Project Overview

This is a Go-based web service called "Impact Resume Service" that provides resume-related functionality. The project follows standard Go project structure with a clean architecture approach.

## Architecture

The project uses a layered architecture with the following structure:

- `cmd/impactresume/main.go` - Application entry point with basic HTTP server setup
- `internal/` - Private application code organized by layers:
  - `config/` - Configuration structures and setup
  - `database/` - Database interfaces and implementations
  - `handlers/` - HTTP request handlers (currently empty)
  - `models/` - Data models and structures (currently empty)  
  - `services/` - Business logic layer (currently empty)
- `pkg/utils/` - Reusable utility packages (currently empty)
- `configs/` - Configuration files (currently empty)
- `docs/` - Documentation (currently empty)
- `scripts/` - Build and deployment scripts (currently empty)
- `test/` - Test files (currently empty)

### Naming Conventions

When creating files in the `internal/` directory:
- **config/**: Files should have `_config` suffix (e.g., `database_config.go`, `server_config.go`)
- **handlers/**: Files should have `_handler` suffix (e.g., `resume_handler.go`, `auth_handler.go`)
- **services/**: Files should have `_service` suffix (e.g., `resume_service.go`, `auth_service.go`)

This naming convention helps distinguish between different layers and makes the codebase more maintainable.

## Development Commands

### Environment Setup
```bash
# Copy the example environment file and configure your local settings
cp .env.example .env

# Edit .env file with your actual OAuth credentials and other settings
# The .env file is automatically loaded in local development
```

### Building and Running
```bash
# Build the application
go build -o bin/impact-resume-service ./cmd/impactresume

# Run the application directly
go run ./cmd/impactresume

# Run with Go's built-in server (development)
go run ./cmd/impactresume
```

### Testing
```bash
# Run all tests
go test ./...

# Run tests with verbose output
go test -v ./...

# Run tests for a specific package
go test ./internal/handlers
```

### Code Quality
```bash
# Format code
go fmt ./...

# Run Go vet for static analysis
go vet ./...

# Run Go mod tidy to clean up dependencies
go mod tidy

# Download dependencies
go mod download
```

### Database Setup
```bash
# Initialize MongoDB database (run once)
./scripts/init-db.sh

# Or run manually
mongosh impact-resume-service scripts/init-db.js
```

## Configuration Management

The application uses a hybrid configuration approach:

### Local Development (APP_ENV=local)
- **Environment Variables**: Loaded from `.env` or `.env.local` files (optional)
- **YAML Configuration**: `configs/local.yaml` for non-sensitive settings
- **Priority**: .env.local > .env > manual environment variables

### Cloud Environments (APP_ENV=dev|prod)
- **AWS Secrets Manager**: Used for sensitive auth credentials
- **YAML Configuration**: `configs/{environment}.yaml` for non-sensitive settings
- **AWS Secret Structure**: JSON format with auth credentials

### Required Environment Variables (Local Development)
```
JWT_SECRET=your-jwt-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
DOMAIN=localhost:8080
```

## Key Technical Details

- **Go Version**: 1.24+ (as specified in go.mod)
- **Module Path**: `github.com/eldon111/impactresume`
- **Default Port**: 8080
- **Database**: MongoDB with collections for users and jobs
- **Current State**: HTTP server with MongoDB integration and OAuth-ready user models

## Development Notes

- The project uses MongoDB for data persistence with proper indexing
- User authentication is designed for GitHub and Google OAuth providers
- JSON API uses camelCase naming conventions
- Database initialization scripts are provided in `scripts/` directory
- The internal packages follow Go conventions with handlers, models, and services separation

## Feature Planning Process

When planning new features:
1. Create a GitHub issue for the main feature
2. Add the feature to the ROADMAP.md file with status, priority, and brief description
3. Create sub-issues for individual tasks if needed
4. Use TodoWrite tool to track implementation progress