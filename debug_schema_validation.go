package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/eldon111/impactresume/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	fmt.Println("🔍 Testing MongoDB schema validation...")

	// Connect to MongoDB
	ctx := context.Background()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI("mongodb://localhost:27017"))
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	defer client.Disconnect(ctx)

	// Get the test database
	db := client.Database("impactresume_test_validation")
	
	// Drop the test collection if it exists
	db.Collection("users").Drop(ctx)

	fmt.Println("✅ Connected to MongoDB")

	// Create collection with the corrected validation schema
	validator := bson.M{
		"$jsonSchema": bson.M{
			"bsonType": "object",
			"required": []string{"_id", "createdAt", "updatedAt"},
			"properties": bson.M{
				"_id": bson.M{
					"bsonType":    "int",
					"description": "User ID must be an integer",
				},
				"createdAt": bson.M{
					"bsonType":    "date",
					"description": "Creation timestamp must be a date",
				},
				"updatedAt": bson.M{
					"bsonType":    "date",
					"description": "Update timestamp must be a date",
				},
				"githubOauth": bson.M{
					"bsonType": "object",
					"properties": bson.M{
						"githubId": bson.M{
							"bsonType":    "string",
							"description": "GitHub ID must be a string",
						},
						"username": bson.M{
							"bsonType":    "string",
							"description": "GitHub username must be a string",
						},
					},
				},
			},
		},
	}

	opts := options.CreateCollection().SetValidator(validator)
	err = db.CreateCollection(ctx, "users", opts)
	if err != nil {
		log.Fatalf("Failed to create collection with validation: %v", err)
	}

	fmt.Println("✅ Created collection with schema validation")

	collection := db.Collection("users")

	// Test 1: Insert a basic user
	fmt.Println("\n🔧 Test 1: Inserting basic user...")
	now := time.Now()
	user := &models.User{
		ID:        1,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// Show what will be inserted
	bsonData, _ := bson.Marshal(user)
	var doc bson.M
	bson.Unmarshal(bsonData, &doc)
	jsonData, _ := json.MarshalIndent(doc, "", "  ")
	fmt.Printf("📋 Document to insert:\n%s\n", jsonData)

	result, err := collection.InsertOne(ctx, user)
	if err != nil {
		fmt.Printf("❌ Failed to insert basic user: %v\n", err)
		if writeErr, ok := err.(mongo.WriteException); ok {
			for _, writeError := range writeErr.WriteErrors {
				fmt.Printf("   Validation error: %s\n", writeError.Message)
			}
		}
	} else {
		fmt.Printf("✅ Basic user inserted successfully with ID: %v\n", result.InsertedID)
	}

	// Test 2: Insert user with GitHub OAuth
	fmt.Println("\n🔧 Test 2: Inserting user with GitHub OAuth...")
	githubOAuth := &models.GitHubOAuth{
		UserID:    2,
		GitHubID:  "12345",
		Username:  "testuser",
		CreatedAt: now,
		UpdatedAt: now,
	}

	userWithOAuth := &models.User{
		ID:          2,
		CreatedAt:   now,
		UpdatedAt:   now,
		GitHubOAuth: githubOAuth,
	}

	bsonData2, _ := bson.Marshal(userWithOAuth)
	var doc2 bson.M
	bson.Unmarshal(bsonData2, &doc2)
	jsonData2, _ := json.MarshalIndent(doc2, "", "  ")
	fmt.Printf("📋 Document to insert:\n%s\n", jsonData2)

	result2, err := collection.InsertOne(ctx, userWithOAuth)
	if err != nil {
		fmt.Printf("❌ Failed to insert user with OAuth: %v\n", err)
		if writeErr, ok := err.(mongo.WriteException); ok {
			for _, writeError := range writeErr.WriteErrors {
				fmt.Printf("   Validation error: %s\n", writeError.Message)
			}
		}
	} else {
		fmt.Printf("✅ User with OAuth inserted successfully with ID: %v\n", result2.InsertedID)
	}

	// Test 3: Try to insert invalid user (missing required fields)
	fmt.Println("\n🔧 Test 3: Inserting invalid user (should fail)...")
	invalidDoc := bson.M{
		"_id": 3,
		// Missing createdAt and updatedAt
	}

	_, err = collection.InsertOne(ctx, invalidDoc)
	if err != nil {
		fmt.Printf("✅ Invalid user correctly rejected: %v\n", err)
	} else {
		fmt.Printf("❌ Invalid user was incorrectly accepted\n")
	}
}
