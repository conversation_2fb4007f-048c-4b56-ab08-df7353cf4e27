basePath: /api/v1
definitions:
  handlers.ClaudeRequest:
    properties:
      model:
        $ref: '#/definitions/services.ClaudeModel'
      prompt:
        type: string
    type: object
  handlers.ClaudeResponse:
    properties:
      model:
        $ref: '#/definitions/services.ClaudeModel'
      response:
        type: string
      usage:
        properties:
          inputTokens:
            type: integer
          outputTokens:
            type: integer
        type: object
    type: object
  handlers.ErrorResponse:
    properties:
      code:
        type: integer
      details:
        items:
          type: string
        type: array
      error:
        type: string
      message:
        type: string
    type: object
  handlers.RefreshRequest:
    properties:
      token:
        type: string
    type: object
  handlers.RefreshResponse:
    properties:
      token:
        type: string
    type: object
  handlers.SuccessResponse:
    properties:
      data: {}
      message:
        type: string
    type: object
  models.DateOnly:
    properties:
      time.Time:
        type: string
    type: object
  models.Job:
    properties:
      company:
        maxLength: 200
        minLength: 1
        type: string
      createdAt:
        type: string
      description:
        maxLength: 5000
        type: string
      endDate:
        $ref: '#/definitions/models.DateOnly'
      id:
        type: string
      isCurrent:
        type: boolean
      location:
        type: string
      startDate:
        $ref: '#/definitions/models.DateOnly'
      title:
        maxLength: 200
        minLength: 1
        type: string
      updatedAt:
        type: string
      userId:
        type: integer
    required:
    - company
    - description
    - startDate
    - title
    type: object
  models.JobCreateRequest:
    properties:
      company:
        maxLength: 200
        minLength: 1
        type: string
      description:
        maxLength: 5000
        type: string
      endDate:
        $ref: '#/definitions/models.DateOnly'
      isCurrent:
        type: boolean
      location:
        type: string
      startDate:
        $ref: '#/definitions/models.DateOnly'
      title:
        maxLength: 200
        minLength: 1
        type: string
    required:
    - company
    - description
    - startDate
    - title
    type: object
  models.JobListResponse:
    properties:
      jobs:
        items:
          $ref: '#/definitions/models.Job'
        type: array
      total:
        type: integer
    type: object
  models.JobProcessRequest:
    properties:
      jobIds:
        items:
          type: string
        minItems: 1
        type: array
      model:
        type: string
    required:
    - jobIds
    type: object
  models.ProcessedJob:
    properties:
      bulletPoints:
        items:
          type: string
        type: array
      jobId:
        type: string
      model:
        type: string
      processedAt:
        type: string
      tokensUsed:
        type: integer
    type: object
  services.ClaudeModel:
    enum:
    - claude-3-haiku-20240307
    - claude-3-sonnet-20240229
    - claude-3-opus-20240229
    - claude-3-5-sonnet-20241022
    - claude-sonnet-4-20250514
    type: string
    x-enum-varnames:
    - Claude3Haiku
    - Claude3Sonnet
    - Claude3Opus
    - Claude35Sonnet
    - Claude4Sonnet
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Resume management and processing service with OAuth authentication
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: ImpactResume API
  version: "1.0"
paths:
  /auth/github/callback:
    get:
      consumes:
      - application/json
      description: Handle GitHub OAuth callback and redirect to frontend with JWT
        token
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to frontend with token
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: GitHub OAuth callback
      tags:
      - auth
  /auth/github/login:
    get:
      consumes:
      - application/json
      description: Initiate GitHub OAuth authentication flow
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to GitHub
          schema:
            type: string
      summary: GitHub OAuth login
      tags:
      - auth
  /auth/google/callback:
    get:
      consumes:
      - application/json
      description: Handle Google OAuth callback and redirect to frontend with JWT
        token
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to frontend with token
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Google OAuth callback
      tags:
      - auth
  /auth/google/login:
    get:
      consumes:
      - application/json
      description: Initiate Google OAuth authentication flow
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to Google
          schema:
            type: string
      summary: Google OAuth login
      tags:
      - auth
  /auth/linkedin/callback:
    get:
      consumes:
      - application/json
      description: Handle LinkedIn OAuth callback and redirect to frontend with JWT
        token
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to frontend with token
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: LinkedIn OAuth callback
      tags:
      - auth
  /auth/linkedin/login:
    get:
      consumes:
      - application/json
      description: Initiate LinkedIn OAuth authentication flow
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to LinkedIn
          schema:
            type: string
      summary: LinkedIn OAuth login
      tags:
      - auth
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: Refresh an existing JWT token
      parameters:
      - description: Refresh token request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.RefreshRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.RefreshResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: Refresh JWT token
      tags:
      - auth
  /internal:
    post:
      consumes:
      - application/json
      description: Internal endpoint for direct access to Claude API
      parameters:
      - description: Claude request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.ClaudeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ClaudeResponse'
        "400":
          description: Bad Request
          schema:
            properties:
              error:
                type: string
            type: object
        "405":
          description: Method Not Allowed
          schema:
            properties:
              error:
                type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            properties:
              error:
                type: string
            type: object
      summary: Direct Claude API access
      tags:
      - internal
  /jobs:
    get:
      consumes:
      - application/json
      description: Retrieve all jobs for the current user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.JobListResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all jobs
      tags:
      - jobs
    post:
      consumes:
      - application/json
      description: Create a new job entry
      parameters:
      - description: Job data
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/models.JobCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Job'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new job
      tags:
      - jobs
  /jobs/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a specific job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete job
      tags:
      - jobs
  /jobs/{id}/description:
    delete:
      consumes:
      - application/json
      description: Delete the description for a specific job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete job description
      tags:
      - jobs
    get:
      consumes:
      - application/json
      description: Retrieve the description for a specific job (user-specific)
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            properties:
              description:
                type: string
              id:
                type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get job description
      tags:
      - jobs
    post:
      consumes:
      - application/json
      description: Create or update the description for a specific job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      - description: Job description
        in: body
        name: description
        required: true
        schema:
          properties:
            description:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create job description
      tags:
      - jobs
    put:
      consumes:
      - application/json
      description: Update the description for a specific job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      - description: Job description
        in: body
        name: description
        required: true
        schema:
          properties:
            description:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update job description
      tags:
      - jobs
  /jobs/{id}/process:
    post:
      consumes:
      - application/json
      description: Process a job description with Claude AI to generate bullet points
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ProcessedJob'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Process a job with AI
      tags:
      - jobs
  /jobs/process:
    post:
      consumes:
      - application/json
      description: Process multiple job descriptions with Claude AI to generate bullet
        points
      parameters:
      - description: Job IDs to process
        in: body
        name: jobs
        required: true
        schema:
          $ref: '#/definitions/models.JobProcessRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            properties:
              processedJobs:
                items:
                  $ref: '#/definitions/models.ProcessedJob'
                type: array
              total:
                type: integer
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Process multiple jobs with AI
      tags:
      - jobs
security:
- BearerAuth: []
securityDefinitions:
  BearerAuth:
    description: 'JWT Authorization header using the Bearer scheme. Example: "Bearer
      {token}"'
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
