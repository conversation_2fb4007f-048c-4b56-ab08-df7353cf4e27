# Error Handling Implementation

This document describes the standardized error handling system implemented for the Impact Resume Service.

## Overview

The error handling system provides:
- **Consistent error types** across all layers
- **Structured error responses** with proper HTTP status codes
- **Centralized error middleware** for automatic error handling
- **Comprehensive logging** with context and severity levels
- **Type-safe error checking** using Go's `errors.Is()` and `errors.As()`

## Architecture

### Error Types

#### AppError
The base error type that all application errors implement:

```go
type AppError struct {
    Code       ErrorCode              `json:"code"`
    Message    string                 `json:"message"`
    Details    string                 `json:"details,omitempty"`
    Severity   ErrorSeverity          `json:"severity"`
    Context    map[string]interface{} `json:"context,omitempty"`
    Cause      error                  `json:"-"`
    HTTPStatus int                    `json:"-"`
}
```

#### ValidationError
Specialized error type for validation failures:

```go
type ValidationError struct {
    *AppError
    Fields []FieldError `json:"fields"`
}
```

### Error Codes

Standardized error codes for different error types:

- **User errors**: `USER_NOT_FOUND`, `USER_ALREADY_EXISTS`, etc.
- **Job errors**: `JOB_NOT_FOUND`, `JOB_CREATION_FAILED`, etc.
- **Validation errors**: `VALIDATION_FAILED`, `INVALID_INPUT`, etc.
- **Authentication errors**: `AUTHENTICATION_FAILED`, `UNAUTHORIZED`, etc.
- **Database errors**: `DATABASE_CONNECTION_ERROR`, `DATABASE_QUERY_ERROR`, etc.

### Error Severity Levels

- **Info**: Informational messages (e.g., resource not found)
- **Warning**: Warning conditions (e.g., validation failures)
- **Error**: Error conditions (e.g., database errors)
- **Critical**: Critical conditions (e.g., system failures)

## Implementation

### Database Layer

Database repositories return structured errors:

```go
func (r *MongoUserRepository) GetUserByID(ctx context.Context, userID int) (*models.User, error) {
    var user models.User
    err := r.collection.FindOne(ctx, bson.M{"_id": userID}).Decode(&user)
    if err != nil {
        if errors.Is(err, mongo.ErrNoDocuments) {
            return nil, apperrors.NewUserNotFoundError(userID)
        }
        return nil, apperrors.NewDatabaseError("get user by ID", err).
            WithContext("userID", userID)
    }
    return &user, nil
}
```

### Service Layer

Services pass through repository errors and add business logic validation:

```go
func (js *JobService) GetJob(ctx context.Context, userID int, jobID string) (*models.Job, error) {
    if jobID == "" {
        return nil, apperrors.NewInvalidInputError("jobID", jobID).
            WithDetails("Job ID cannot be empty")
    }

    job, err := js.jobRepo.GetJobByID(ctx, userID, jobID)
    if err != nil {
        return nil, err // Repository already returns proper error types
    }

    return job, nil
}
```

### Handler Layer

Handlers return domain errors and let middleware handle HTTP conversion:

```go
func (jh *JobHandler) GetJob(c echo.Context) error {
    userID := getUserIDFromContext(c)
    jobID := c.Param("id")
    
    if jobID == "" {
        return apperrors.NewInvalidInputError("jobID", jobID).
            WithDetails("Job ID parameter is required")
    }

    job, err := jh.jobService.GetJob(c.Request().Context(), userID, jobID)
    if err != nil {
        return err // Let middleware handle conversion
    }

    return c.JSON(http.StatusOK, job)
}
```

### Error Middleware

Centralized error handling middleware automatically converts errors to HTTP responses:

```go
func ErrorMiddleware() echo.MiddlewareFunc {
    return func(next echo.HandlerFunc) echo.HandlerFunc {
        return func(c echo.Context) error {
            err := next(c)
            if err != nil {
                return handleError(c, err, cfg, requestID)
            }
            return nil
        }
    }
}
```

## Error Response Format

All errors return a consistent JSON structure:

```json
{
    "error": "Not Found",
    "code": "USER_NOT_FOUND",
    "message": "User not found",
    "details": "User with ID 123 does not exist",
    "fields": [
        {
            "field": "email",
            "value": "invalid-email",
            "tag": "email",
            "message": "email must be a valid email address"
        }
    ],
    "context": {
        "userID": 123,
        "operation": "get_user"
    },
    "requestId": "req_1234567890",
    "timestamp": "2023-07-11T10:30:00Z"
}
```

## Error Checking

Use Go's standard error checking patterns:

```go
// Check for specific error types
var appErr *apperrors.AppError
if errors.As(err, &appErr) && appErr.Code == apperrors.ErrCodeUserNotFound {
    // Handle user not found
}

// Check for validation errors
var validationErr *apperrors.ValidationError
if errors.As(err, &validationErr) {
    // Handle validation errors
    for _, field := range validationErr.Fields {
        log.Printf("Field %s: %s", field.Field, field.Message)
    }
}
```

## Logging

Errors are automatically logged with appropriate severity levels:

```
ERROR: [error] USER_NOT_FOUND: User not found | Context: map[errorCode:USER_NOT_FOUND method:GET path:/api/v1/users/123 remoteAddr:*********** requestId:req_1234567890 severity:error userAgent:Mozilla/5.0 userID:123]
```

## Configuration

Error middleware can be configured:

```go
config := middleware.ErrorMiddlewareConfig{
    IncludeStackTrace: true,
    IncludeContext:    false, // Don't expose internal context in production
    LogLevel:          apperrors.SeverityWarning,
    RequestIDHeader:   "X-Request-ID",
}

e.Use(middleware.ErrorMiddleware(config))
e.HTTPErrorHandler = middleware.ErrorHandler(config)
```

## Testing

Comprehensive tests cover:
- Error type creation and properties
- Error code to HTTP status mapping
- Validation error formatting
- Middleware error handling
- Error logging and context

## Migration Guide

### From Old Error Handling

**Before:**
```go
if err == database.ErrUserNotFound {
    return echo.NewHTTPError(http.StatusNotFound, "User not found")
}
```

**After:**
```go
// Repository returns structured error
return apperrors.NewUserNotFoundError(userID)

// Handler lets middleware handle conversion
return err
```

### Error Checking

**Before:**
```go
if err == database.ErrUserNotFound {
    // Handle error
}
```

**After:**
```go
var appErr *apperrors.AppError
if errors.As(err, &appErr) && appErr.Code == apperrors.ErrCodeUserNotFound {
    // Handle error
}
```

## Benefits

1. **Consistency**: All errors follow the same structure and patterns
2. **Type Safety**: Compile-time error checking with proper types
3. **Debugging**: Rich context and logging for troubleshooting
4. **API Consistency**: Standardized error responses for clients
5. **Maintainability**: Centralized error handling logic
6. **Extensibility**: Easy to add new error types and codes

## Best Practices

1. **Use specific error codes** for different error conditions
2. **Add context** to errors with relevant information
3. **Use appropriate severity levels** for logging
4. **Don't expose internal details** in error messages to clients
5. **Test error handling paths** thoroughly
6. **Use errors.Is() and errors.As()** for error checking
7. **Let middleware handle HTTP conversion** in handlers
