package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/eldon111/impactresume/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	fmt.Println("🔍 Debugging MongoDB insertion for User model...")

	// Connect to MongoDB
	ctx := context.Background()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI("mongodb://localhost:27017"))
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	defer client.Disconnect(ctx)

	// Get the test database and collection
	db := client.Database("impactresume_test")
	collection := db.Collection("users_test")

	// Drop the test collection if it exists
	collection.Drop(ctx)

	fmt.Println("✅ Connected to MongoDB")

	// Create a test user
	now := time.Now()
	user := &models.User{
		ID:        1,
		CreatedAt: now,
		UpdatedAt: now,
	}

	fmt.Printf("📅 Creating user with time: %v\n", now)

	// Test what gets serialized
	bsonData, err := bson.Marshal(user)
	if err != nil {
		log.Fatalf("Failed to marshal to BSON: %v", err)
	}

	var doc bson.M
	err = bson.Unmarshal(bsonData, &doc)
	if err != nil {
		log.Fatalf("Failed to unmarshal to bson.M: %v", err)
	}

	jsonData, err := json.MarshalIndent(doc, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal to JSON: %v", err)
	}

	fmt.Printf("📋 Document to be inserted:\n%s\n", jsonData)

	// Try to insert the user
	fmt.Println("🔧 Attempting to insert user...")
	result, err := collection.InsertOne(ctx, user)
	if err != nil {
		fmt.Printf("❌ Failed to insert user: %v\n", err)
		
		// Check if it's a validation error
		if writeErr, ok := err.(mongo.WriteException); ok {
			for _, writeError := range writeErr.WriteErrors {
				fmt.Printf("   Write error code: %d\n", writeError.Code)
				fmt.Printf("   Write error message: %s\n", writeError.Message)
			}
		}
		return
	}

	fmt.Printf("✅ User inserted successfully with ID: %v\n", result.InsertedID)

	// Try to retrieve the user
	fmt.Println("🔧 Attempting to retrieve user...")
	var retrievedUser models.User
	err = collection.FindOne(ctx, bson.M{"_id": 1}).Decode(&retrievedUser)
	if err != nil {
		fmt.Printf("❌ Failed to retrieve user: %v\n", err)
		return
	}

	fmt.Printf("✅ User retrieved successfully\n")
	fmt.Printf("📅 Retrieved CreatedAt: %v\n", retrievedUser.CreatedAt)
	fmt.Printf("📅 Retrieved UpdatedAt: %v\n", retrievedUser.UpdatedAt)

	// Test with GitHub OAuth
	fmt.Println("\n🔧 Testing with GitHub OAuth...")
	githubOAuth := &models.GitHubOAuth{
		UserID:    2,
		GitHubID:  "12345",
		Username:  "testuser",
		CreatedAt: now,
		UpdatedAt: now,
	}

	userWithOAuth := &models.User{
		ID:          2,
		CreatedAt:   now,
		UpdatedAt:   now,
		GitHubOAuth: githubOAuth,
	}

	result2, err := collection.InsertOne(ctx, userWithOAuth)
	if err != nil {
		fmt.Printf("❌ Failed to insert user with OAuth: %v\n", err)
		return
	}

	fmt.Printf("✅ User with OAuth inserted successfully with ID: %v\n", result2.InsertedID)
}
