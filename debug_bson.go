package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/eldon111/impactresume/internal/models"
	"go.mongodb.org/mongo-driver/bson"
)

func main() {
	fmt.Println("🔍 Debugging BSON serialization for User model...")

	// Create a test user with time fields
	now := time.Now()
	user := &models.User{
		ID:        1,
		CreatedAt: now,
		UpdatedAt: now,
	}

	fmt.Printf("📅 Original time: %v\n", now)
	fmt.Printf("📅 User.CreatedAt: %v\n", user.CreatedAt)
	fmt.Printf("📅 User.UpdatedAt: %v\n", user.UpdatedAt)

	// Test BSON marshaling
	fmt.Println("\n🔧 Testing BSON marshaling...")
	bsonData, err := bson.Marshal(user)
	if err != nil {
		log.Fatalf("Failed to marshal to BSON: %v", err)
	}

	fmt.Printf("✅ BSON marshaling successful, size: %d bytes\n", len(bsonData))

	// Test BSON unmarshaling
	fmt.Println("\n🔧 Testing BSON unmarshaling...")
	var unmarshaledUser models.User
	err = bson.Unmarshal(bsonData, &unmarshaledUser)
	if err != nil {
		log.Fatalf("Failed to unmarshal from BSON: %v", err)
	}

	fmt.Printf("✅ BSON unmarshaling successful\n")
	fmt.Printf("📅 Unmarshaled CreatedAt: %v\n", unmarshaledUser.CreatedAt)
	fmt.Printf("📅 Unmarshaled UpdatedAt: %v\n", unmarshaledUser.UpdatedAt)

	// Convert BSON to readable format
	fmt.Println("\n📋 BSON document structure:")
	var doc bson.M
	err = bson.Unmarshal(bsonData, &doc)
	if err != nil {
		log.Fatalf("Failed to unmarshal to bson.M: %v", err)
	}

	jsonData, err := json.MarshalIndent(doc, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal to JSON: %v", err)
	}

	fmt.Printf("%s\n", jsonData)

	// Test with GitHub OAuth
	fmt.Println("\n🔧 Testing with GitHub OAuth...")
	githubOAuth := &models.GitHubOAuth{
		UserID:    1,
		GitHubID:  "12345",
		Username:  "testuser",
		CreatedAt: now,
		UpdatedAt: now,
	}

	userWithOAuth := &models.User{
		ID:          1,
		CreatedAt:   now,
		UpdatedAt:   now,
		GitHubOAuth: githubOAuth,
	}

	bsonDataWithOAuth, err := bson.Marshal(userWithOAuth)
	if err != nil {
		log.Fatalf("Failed to marshal user with OAuth to BSON: %v", err)
	}

	var docWithOAuth bson.M
	err = bson.Unmarshal(bsonDataWithOAuth, &docWithOAuth)
	if err != nil {
		log.Fatalf("Failed to unmarshal user with OAuth to bson.M: %v", err)
	}

	jsonDataWithOAuth, err := json.MarshalIndent(docWithOAuth, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal user with OAuth to JSON: %v", err)
	}

	fmt.Printf("📋 User with OAuth BSON structure:\n%s\n", jsonDataWithOAuth)
}
