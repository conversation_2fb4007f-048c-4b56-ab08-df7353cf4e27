# Environment configuration for local development
# Copy this file to .env and fill in your actual values
# Or copy to .env.local for local overrides

# Application Environment
# Set to "local" for local development (uses .env files and configs/local.yaml)
# Set to "dev" for development environment (uses AWS Secrets Manager)
# Set to "prod" for production environment (uses AWS Secrets Manager)
APP_ENV=local

# JWT Configuration
# Secret key for signing JWT tokens - use a strong, random secret
JWT_SECRET=your-jwt-secret-key-here

# OAuth Provider Configuration
# GitHub OAuth Application
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth Application
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# LinkedIn OAuth Application
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Application Domain
# Used for OAuth callback URLs
# For local development, typically "localhost:8080"
# For production, use your actual domain
DOMAIN=localhost:8080

# Frontend URL
# Used for redirecting users after OAuth authentication
# For local development, typically "http://localhost:5173" (Vite) or "http://localhost:3000" (React)
# For production, use your actual frontend domain
FRONTEND_URL=http://localhost:5173

# AWS Configuration (only needed if testing AWS integration locally)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=us-east-1

# Database Configuration (handled by configs/local.yaml)
# MONGO_URI=mongodb://localhost:27017
# MONGO_DATABASE=impact-resume-service

# Claude API Configuration (handled by configs/local.yaml)
# CLAUDE_API_KEY=your-claude-api-key