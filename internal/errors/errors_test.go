package errors

import (
	"errors"
	"net/http"
	"testing"
)

func TestAppError_Error(t *testing.T) {
	tests := []struct {
		name     string
		appErr   *AppError
		expected string
	}{
		{
			name: "Error with details",
			appErr: &AppError{
				Code:    ErrCodeUserNotFound,
				Message: "User not found",
				Details: "User with ID 123 does not exist",
			},
			expected: "USER_NOT_FOUND: User not found - User with ID 123 does not exist",
		},
		{
			name: "Error without details",
			appErr: &AppError{
				Code:    ErrCodeValidationFailed,
				Message: "Validation failed",
			},
			expected: "VALIDATION_FAILED: Validation failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.appErr.Error(); got != tt.expected {
				t.Errorf("AppError.Error() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestAppError_Is(t *testing.T) {
	userNotFoundErr1 := NewUserNotFoundError(123)
	userNotFoundErr2 := NewUserNotFoundError(456)
	validationErr := NewValidationError("test validation error")

	tests := []struct {
		name     string
		err      *AppError
		target   error
		expected bool
	}{
		{
			name:     "Same error code should match",
			err:      userNotFoundErr1,
			target:   userNotFoundErr2,
			expected: true,
		},
		{
			name:     "Different error codes should not match",
			err:      userNotFoundErr1,
			target:   validationErr,
			expected: false,
		},
		{
			name:     "Non-AppError should not match",
			err:      userNotFoundErr1,
			target:   errors.New("some other error"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.err.Is(tt.target); got != tt.expected {
				t.Errorf("AppError.Is() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestAppError_WithContext(t *testing.T) {
	err := NewUserNotFoundError(123)
	err = err.WithContext("operation", "get_user")

	if err.Context == nil {
		t.Error("Expected context to be set")
	}

	if err.Context["userID"] != 123 {
		t.Errorf("Expected userID to be 123, got %v", err.Context["userID"])
	}

	if err.Context["operation"] != "get_user" {
		t.Errorf("Expected operation to be 'get_user', got %v", err.Context["operation"])
	}
}

func TestAppError_WithDetails(t *testing.T) {
	err := NewUserNotFoundError(123)
	details := "Additional error details"
	err = err.WithDetails(details)

	if err.Details != details {
		t.Errorf("Expected details to be '%s', got '%s'", details, err.Details)
	}
}

func TestAppError_WithCause(t *testing.T) {
	originalErr := errors.New("original error")
	err := NewInternalError("Internal error occurred", nil)
	err = err.WithCause(originalErr)

	if err.Cause != originalErr {
		t.Errorf("Expected cause to be set to original error")
	}

	if err.Unwrap() != originalErr {
		t.Errorf("Expected Unwrap() to return original error")
	}
}

func TestGetHTTPStatusForCode(t *testing.T) {
	tests := []struct {
		name     string
		code     ErrorCode
		expected int
	}{
		{
			name:     "User not found should return 404",
			code:     ErrCodeUserNotFound,
			expected: http.StatusNotFound,
		},
		{
			name:     "Validation failed should return 400",
			code:     ErrCodeValidationFailed,
			expected: http.StatusBadRequest,
		},
		{
			name:     "Authentication failed should return 401",
			code:     ErrCodeAuthenticationFailed,
			expected: http.StatusUnauthorized,
		},
		{
			name:     "Forbidden should return 403",
			code:     ErrCodeForbidden,
			expected: http.StatusForbidden,
		},
		{
			name:     "User already exists should return 409",
			code:     ErrCodeUserAlreadyExists,
			expected: http.StatusConflict,
		},
		{
			name:     "Internal error should return 500",
			code:     ErrCodeInternalError,
			expected: http.StatusInternalServerError,
		},
		{
			name:     "Unknown error code should return 500",
			code:     ErrorCode("UNKNOWN_ERROR"),
			expected: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getHTTPStatusForCode(tt.code); got != tt.expected {
				t.Errorf("getHTTPStatusForCode() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestNewUserNotFoundError(t *testing.T) {
	userID := 123
	err := NewUserNotFoundError(userID)

	if err.Code != ErrCodeUserNotFound {
		t.Errorf("Expected error code %s, got %s", ErrCodeUserNotFound, err.Code)
	}

	if err.Message != "User not found" {
		t.Errorf("Expected message 'User not found', got '%s'", err.Message)
	}

	if err.Context["userID"] != userID {
		t.Errorf("Expected userID context to be %d, got %v", userID, err.Context["userID"])
	}

	if err.HTTPStatus != http.StatusNotFound {
		t.Errorf("Expected HTTP status %d, got %d", http.StatusNotFound, err.HTTPStatus)
	}
}

func TestNewJobNotFoundError(t *testing.T) {
	jobID := "job_123"
	userID := 456
	err := NewJobNotFoundError(jobID, userID)

	if err.Code != ErrCodeJobNotFound {
		t.Errorf("Expected error code %s, got %s", ErrCodeJobNotFound, err.Code)
	}

	if err.Message != "Job not found" {
		t.Errorf("Expected message 'Job not found', got '%s'", err.Message)
	}

	if err.Context["jobID"] != jobID {
		t.Errorf("Expected jobID context to be %s, got %v", jobID, err.Context["jobID"])
	}

	if err.Context["userID"] != userID {
		t.Errorf("Expected userID context to be %d, got %v", userID, err.Context["userID"])
	}
}

func TestNewValidationError(t *testing.T) {
	message := "Validation failed"
	err := NewValidationError(message)

	if err.Code != ErrCodeValidationFailed {
		t.Errorf("Expected error code %s, got %s", ErrCodeValidationFailed, err.Code)
	}

	if err.Message != message {
		t.Errorf("Expected message '%s', got '%s'", message, err.Message)
	}

	if err.HTTPStatus != http.StatusBadRequest {
		t.Errorf("Expected HTTP status %d, got %d", http.StatusBadRequest, err.HTTPStatus)
	}
}

func TestNewInvalidInputError(t *testing.T) {
	field := "email"
	value := "invalid-email"
	err := NewInvalidInputError(field, value)

	if err.Code != ErrCodeInvalidInput {
		t.Errorf("Expected error code %s, got %s", ErrCodeInvalidInput, err.Code)
	}

	expectedMessage := "Invalid input for field 'email'"
	if err.Message != expectedMessage {
		t.Errorf("Expected message '%s', got '%s'", expectedMessage, err.Message)
	}

	if err.Context["field"] != field {
		t.Errorf("Expected field context to be %s, got %v", field, err.Context["field"])
	}

	if err.Context["value"] != value {
		t.Errorf("Expected value context to be %s, got %v", value, err.Context["value"])
	}
}

func TestNewAuthenticationError(t *testing.T) {
	reason := "Invalid credentials"
	err := NewAuthenticationError(reason)

	if err.Code != ErrCodeAuthenticationFailed {
		t.Errorf("Expected error code %s, got %s", ErrCodeAuthenticationFailed, err.Code)
	}

	if err.Message != "Authentication failed" {
		t.Errorf("Expected message 'Authentication failed', got '%s'", err.Message)
	}

	if err.Details != reason {
		t.Errorf("Expected details '%s', got '%s'", reason, err.Details)
	}

	if err.HTTPStatus != http.StatusUnauthorized {
		t.Errorf("Expected HTTP status %d, got %d", http.StatusUnauthorized, err.HTTPStatus)
	}
}

func TestNewDatabaseError(t *testing.T) {
	operation := "create_user"
	originalErr := errors.New("connection failed")
	err := NewDatabaseError(operation, originalErr)

	if err.Code != ErrCodeDatabaseQuery {
		t.Errorf("Expected error code %s, got %s", ErrCodeDatabaseQuery, err.Code)
	}

	expectedMessage := "Database operation failed: create_user"
	if err.Message != expectedMessage {
		t.Errorf("Expected message '%s', got '%s'", expectedMessage, err.Message)
	}

	if err.Cause != originalErr {
		t.Errorf("Expected cause to be original error")
	}

	if err.Context["operation"] != operation {
		t.Errorf("Expected operation context to be %s, got %v", operation, err.Context["operation"])
	}
}
