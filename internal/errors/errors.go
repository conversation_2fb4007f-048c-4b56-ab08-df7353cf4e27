package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode represents a unique error code for each error type
type ErrorCode string

const (
	// User-related errors
	ErrCodeUserNotFound       ErrorCode = "USER_NOT_FOUND"
	ErrCodeUserAlreadyExists  ErrorCode = "USER_ALREADY_EXISTS"
	ErrCodeUserCreationFailed ErrorCode = "USER_CREATION_FAILED"
	ErrCodeUserUpdateFailed   ErrorCode = "USER_UPDATE_FAILED"

	// Job-related errors
	ErrCodeJobNotFound       ErrorCode = "JOB_NOT_FOUND"
	ErrCodeJobAlreadyExists  ErrorCode = "JOB_ALREADY_EXISTS"
	ErrCodeJobCreationFailed ErrorCode = "JOB_CREATION_FAILED"
	ErrCodeJobUpdateFailed   ErrorCode = "JOB_UPDATE_FAILED"
	ErrCodeJobDeletionFailed ErrorCode = "JOB_DELETION_FAILED"

	// Validation errors
	ErrCodeValidationFailed ErrorCode = "VALIDATION_FAILED"
	ErrCodeInvalidInput     ErrorCode = "INVALID_INPUT"
	ErrCodeMissingField     ErrorCode = "MISSING_FIELD"
	ErrCodeInvalidFormat    ErrorCode = "INVALID_FORMAT"

	// Authentication errors
	ErrCodeAuthenticationFailed ErrorCode = "AUTHENTICATION_FAILED"
	ErrCodeInvalidToken         ErrorCode = "INVALID_TOKEN"
	ErrCodeTokenExpired         ErrorCode = "TOKEN_EXPIRED"
	ErrCodeUnauthorized         ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden            ErrorCode = "FORBIDDEN"

	// OAuth errors
	ErrCodeOAuthFailed         ErrorCode = "OAUTH_FAILED"
	ErrCodeOAuthProviderError  ErrorCode = "OAUTH_PROVIDER_ERROR"
	ErrCodeOAuthCallbackFailed ErrorCode = "OAUTH_CALLBACK_FAILED"

	// Database errors
	ErrCodeDatabaseConnection ErrorCode = "DATABASE_CONNECTION_ERROR"
	ErrCodeDatabaseQuery      ErrorCode = "DATABASE_QUERY_ERROR"
	ErrCodeDatabaseConstraint ErrorCode = "DATABASE_CONSTRAINT_ERROR"

	// External service errors
	ErrCodeExternalService   ErrorCode = "EXTERNAL_SERVICE_ERROR"
	ErrCodeClaudeAPIError    ErrorCode = "CLAUDE_API_ERROR"
	ErrCodeRateLimitExceeded ErrorCode = "RATE_LIMIT_EXCEEDED"

	// Internal errors
	ErrCodeInternalError      ErrorCode = "INTERNAL_ERROR"
	ErrCodeConfigurationError ErrorCode = "CONFIGURATION_ERROR"
	ErrCodeNotImplemented     ErrorCode = "NOT_IMPLEMENTED"
)

// ErrorSeverity represents the severity level of an error
type ErrorSeverity string

const (
	SeverityInfo     ErrorSeverity = "info"
	SeverityWarning  ErrorSeverity = "warning"
	SeverityError    ErrorSeverity = "error"
	SeverityCritical ErrorSeverity = "critical"
)

// AppError represents a structured application error
type AppError struct {
	Code       ErrorCode              `json:"code"`
	Message    string                 `json:"message"`
	Details    string                 `json:"details,omitempty"`
	Severity   ErrorSeverity          `json:"severity"`
	Context    map[string]interface{} `json:"context,omitempty"`
	Cause      error                  `json:"-"` // Original error, not serialized
	HTTPStatus int                    `json:"-"` // HTTP status code, not serialized
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s - %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap returns the underlying error for error wrapping
func (e *AppError) Unwrap() error {
	return e.Cause
}

// Is implements error comparison for errors.Is()
func (e *AppError) Is(target error) bool {
	if t, ok := target.(*AppError); ok {
		return e.Code == t.Code
	}
	return false
}

// WithContext adds context information to the error
func (e *AppError) WithContext(key string, value interface{}) *AppError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// WithDetails adds additional details to the error
func (e *AppError) WithDetails(details string) *AppError {
	e.Details = details
	return e
}

// WithCause wraps an underlying error
func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

// NewAppError creates a new application error
func NewAppError(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Severity:   SeverityError,
		HTTPStatus: getHTTPStatusForCode(code),
	}
}

// NewAppErrorWithSeverity creates a new application error with specified severity
func NewAppErrorWithSeverity(code ErrorCode, message string, severity ErrorSeverity) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Severity:   severity,
		HTTPStatus: getHTTPStatusForCode(code),
	}
}

// getHTTPStatusForCode maps error codes to HTTP status codes
func getHTTPStatusForCode(code ErrorCode) int {
	switch code {
	// 400 Bad Request
	case ErrCodeValidationFailed, ErrCodeInvalidInput, ErrCodeMissingField, ErrCodeInvalidFormat:
		return http.StatusBadRequest

	// 401 Unauthorized
	case ErrCodeAuthenticationFailed, ErrCodeInvalidToken, ErrCodeTokenExpired, ErrCodeUnauthorized:
		return http.StatusUnauthorized

	// 403 Forbidden
	case ErrCodeForbidden:
		return http.StatusForbidden

	// 404 Not Found
	case ErrCodeUserNotFound, ErrCodeJobNotFound:
		return http.StatusNotFound

	// 409 Conflict
	case ErrCodeUserAlreadyExists, ErrCodeJobAlreadyExists:
		return http.StatusConflict

	// 422 Unprocessable Entity
	case ErrCodeDatabaseConstraint:
		return http.StatusUnprocessableEntity

	// 429 Too Many Requests
	case ErrCodeRateLimitExceeded:
		return http.StatusTooManyRequests

	// 500 Internal Server Error
	case ErrCodeInternalError, ErrCodeDatabaseConnection, ErrCodeDatabaseQuery,
		ErrCodeExternalService, ErrCodeClaudeAPIError, ErrCodeConfigurationError:
		return http.StatusInternalServerError

	// 501 Not Implemented
	case ErrCodeNotImplemented:
		return http.StatusNotImplemented

	// 502 Bad Gateway
	case ErrCodeOAuthFailed, ErrCodeOAuthProviderError, ErrCodeOAuthCallbackFailed:
		return http.StatusBadGateway

	// 503 Service Unavailable
	case ErrCodeUserCreationFailed, ErrCodeUserUpdateFailed, ErrCodeJobCreationFailed,
		ErrCodeJobUpdateFailed, ErrCodeJobDeletionFailed:
		return http.StatusServiceUnavailable

	default:
		return http.StatusInternalServerError
	}
}

// Predefined error constructors for common errors

// User errors
func NewUserNotFoundError(userID interface{}) *AppError {
	return NewAppError(ErrCodeUserNotFound, "User not found").
		WithContext("userID", userID)
}

func NewUserAlreadyExistsError(identifier string) *AppError {
	return NewAppError(ErrCodeUserAlreadyExists, "User already exists").
		WithContext("identifier", identifier)
}

// Job errors
func NewJobNotFoundError(jobID string, userID interface{}) *AppError {
	return NewAppError(ErrCodeJobNotFound, "Job not found").
		WithContext("jobID", jobID).
		WithContext("userID", userID)
}

func NewJobAlreadyExistsError(jobID string) *AppError {
	return NewAppError(ErrCodeJobAlreadyExists, "Job already exists").
		WithContext("jobID", jobID)
}

// Validation errors
func NewValidationError(message string) *AppError {
	return NewAppError(ErrCodeValidationFailed, message)
}

func NewInvalidInputError(field string, value interface{}) *AppError {
	return NewAppError(ErrCodeInvalidInput, fmt.Sprintf("Invalid input for field '%s'", field)).
		WithContext("field", field).
		WithContext("value", value)
}

// Authentication errors
func NewAuthenticationError(reason string) *AppError {
	return NewAppError(ErrCodeAuthenticationFailed, "Authentication failed").
		WithDetails(reason)
}

func NewUnauthorizedError(message string) *AppError {
	return NewAppError(ErrCodeUnauthorized, message)
}

func NewForbiddenError(message string) *AppError {
	return NewAppError(ErrCodeForbidden, message)
}

// OAuth errors
func NewOAuthError(provider string, reason string) *AppError {
	return NewAppError(ErrCodeOAuthFailed, "OAuth authentication failed").
		WithContext("provider", provider).
		WithDetails(reason)
}

// Database errors
func NewDatabaseError(operation string, cause error) *AppError {
	return NewAppError(ErrCodeDatabaseQuery, fmt.Sprintf("Database operation failed: %s", operation)).
		WithCause(cause).
		WithContext("operation", operation)
}

// External service errors
func NewExternalServiceError(service string, cause error) *AppError {
	return NewAppError(ErrCodeExternalService, fmt.Sprintf("External service error: %s", service)).
		WithCause(cause).
		WithContext("service", service)
}

func NewClaudeAPIError(cause error) *AppError {
	return NewAppError(ErrCodeClaudeAPIError, "Claude API request failed").
		WithCause(cause)
}

// Internal errors
func NewInternalError(message string, cause error) *AppError {
	return NewAppErrorWithSeverity(ErrCodeInternalError, message, SeverityCritical).
		WithCause(cause)
}

func NewConfigurationError(setting string, cause error) *AppError {
	return NewAppErrorWithSeverity(ErrCodeConfigurationError, fmt.Sprintf("Configuration error: %s", setting), SeverityCritical).
		WithCause(cause).
		WithContext("setting", setting)
}
