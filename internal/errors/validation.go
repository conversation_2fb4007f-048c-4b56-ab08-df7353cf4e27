package errors

import (
	"fmt"
	"strings"

	"github.com/go-playground/validator/v10"
)

// ValidationError represents a structured validation error
type ValidationError struct {
	*AppError
	Fields []FieldError `json:"fields"`
}

// FieldError represents a validation error for a specific field
type FieldError struct {
	Field   string      `json:"field"`
	Value   interface{} `json:"value,omitempty"`
	Tag     string      `json:"tag"`
	Message string      `json:"message"`
	Param   string      `json:"param,omitempty"`
}

// NewValidationErrorFromValidator creates a ValidationError from validator.ValidationErrors
func NewValidationErrorFromValidator(err error) *ValidationError {
	validationErr := &ValidationError{
		AppError: NewAppError(ErrCodeValidationFailed, "Validation failed"),
		Fields:   []FieldError{},
	}

	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrors {
			fieldErr := FieldError{
				Field:   strings.ToLower(e.Field()),
				Value:   e.Value(),
				Tag:     e.Tag(),
				Param:   e.Param(),
				Message: formatValidationMessage(e),
			}
			validationErr.Fields = append(validationErr.Fields, fieldErr)
		}
	}

	return validationErr
}

// NewValidationErrorFromFields creates a ValidationError from field errors
func NewValidationErrorFromFields(fields []FieldError) *ValidationError {
	return &ValidationError{
		AppError: NewAppError(ErrCodeValidationFailed, "Validation failed"),
		Fields:   fields,
	}
}

// NewSingleFieldValidationError creates a ValidationError for a single field
func NewSingleFieldValidationError(field, message string, value interface{}) *ValidationError {
	fieldErr := FieldError{
		Field:   field,
		Value:   value,
		Message: message,
	}

	return &ValidationError{
		AppError: NewAppError(ErrCodeValidationFailed, "Validation failed"),
		Fields:   []FieldError{fieldErr},
	}
}

// Error returns a formatted error message
func (v *ValidationError) Error() string {
	if len(v.Fields) == 0 {
		return v.AppError.Error()
	}

	var messages []string
	for _, field := range v.Fields {
		messages = append(messages, field.Message)
	}

	return fmt.Sprintf("%s: %s", v.AppError.Error(), strings.Join(messages, "; "))
}

// AddField adds a field error to the validation error
func (v *ValidationError) AddField(field FieldError) *ValidationError {
	v.Fields = append(v.Fields, field)
	return v
}

// HasField checks if a specific field has a validation error
func (v *ValidationError) HasField(fieldName string) bool {
	for _, field := range v.Fields {
		if field.Field == fieldName {
			return true
		}
	}
	return false
}

// GetFieldError returns the validation error for a specific field
func (v *ValidationError) GetFieldError(fieldName string) *FieldError {
	for _, field := range v.Fields {
		if field.Field == fieldName {
			return &field
		}
	}
	return nil
}

// GetFieldMessages returns all error messages for validation errors
func (v *ValidationError) GetFieldMessages() []string {
	var messages []string
	for _, field := range v.Fields {
		messages = append(messages, field.Message)
	}
	return messages
}

// formatValidationMessage formats a validation error message based on the tag
func formatValidationMessage(e validator.FieldError) string {
	field := strings.ToLower(e.Field())

	switch e.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", field)
	case "min":
		if e.Type().String() == "string" {
			return fmt.Sprintf("%s must be at least %s characters long", field, e.Param())
		}
		return fmt.Sprintf("%s must be at least %s", field, e.Param())
	case "max":
		if e.Type().String() == "string" {
			return fmt.Sprintf("%s must be at most %s characters long", field, e.Param())
		}
		return fmt.Sprintf("%s must be at most %s", field, e.Param())
	case "len":
		return fmt.Sprintf("%s must be exactly %s characters long", field, e.Param())
	case "email":
		return fmt.Sprintf("%s must be a valid email address", field)
	case "url":
		return fmt.Sprintf("%s must be a valid URL", field)
	case "uuid":
		return fmt.Sprintf("%s must be a valid UUID", field)
	case "alpha":
		return fmt.Sprintf("%s must contain only alphabetic characters", field)
	case "alphanum":
		return fmt.Sprintf("%s must contain only alphanumeric characters", field)
	case "numeric":
		return fmt.Sprintf("%s must be numeric", field)
	case "oneof":
		return fmt.Sprintf("%s must be one of: %s", field, e.Param())
	case "gt":
		return fmt.Sprintf("%s must be greater than %s", field, e.Param())
	case "gte":
		return fmt.Sprintf("%s must be greater than or equal to %s", field, e.Param())
	case "lt":
		return fmt.Sprintf("%s must be less than %s", field, e.Param())
	case "lte":
		return fmt.Sprintf("%s must be less than or equal to %s", field, e.Param())
	case "eq":
		return fmt.Sprintf("%s must be equal to %s", field, e.Param())
	case "ne":
		return fmt.Sprintf("%s must not be equal to %s", field, e.Param())
	case "date_range":
		return "end date must be after start date"
	case "datetime":
		return fmt.Sprintf("%s must be a valid date and time", field)
	case "json":
		return fmt.Sprintf("%s must be valid JSON", field)
	case "base64":
		return fmt.Sprintf("%s must be valid base64", field)
	case "hexadecimal":
		return fmt.Sprintf("%s must be a valid hexadecimal", field)
	case "rgb":
		return fmt.Sprintf("%s must be a valid RGB color", field)
	case "rgba":
		return fmt.Sprintf("%s must be a valid RGBA color", field)
	case "hsl":
		return fmt.Sprintf("%s must be a valid HSL color", field)
	case "hsla":
		return fmt.Sprintf("%s must be a valid HSLA color", field)
	case "ip":
		return fmt.Sprintf("%s must be a valid IP address", field)
	case "ipv4":
		return fmt.Sprintf("%s must be a valid IPv4 address", field)
	case "ipv6":
		return fmt.Sprintf("%s must be a valid IPv6 address", field)
	case "mac":
		return fmt.Sprintf("%s must be a valid MAC address", field)
	case "latitude":
		return fmt.Sprintf("%s must be a valid latitude", field)
	case "longitude":
		return fmt.Sprintf("%s must be a valid longitude", field)
	case "ssn":
		return fmt.Sprintf("%s must be a valid SSN", field)
	case "credit_card":
		return fmt.Sprintf("%s must be a valid credit card number", field)
	case "isbn":
		return fmt.Sprintf("%s must be a valid ISBN", field)
	case "isbn10":
		return fmt.Sprintf("%s must be a valid ISBN-10", field)
	case "isbn13":
		return fmt.Sprintf("%s must be a valid ISBN-13", field)
	default:
		return fmt.Sprintf("%s is invalid", field)
	}
}

// Common validation error constructors

// NewRequiredFieldError creates a validation error for a required field
func NewRequiredFieldError(field string) *ValidationError {
	return NewSingleFieldValidationError(field, fmt.Sprintf("%s is required", field), nil)
}

// NewInvalidFormatError creates a validation error for invalid format
func NewInvalidFormatError(field string, expectedFormat string, value interface{}) *ValidationError {
	message := fmt.Sprintf("%s must be in %s format", field, expectedFormat)
	return NewSingleFieldValidationError(field, message, value)
}

// NewInvalidLengthError creates a validation error for invalid length
func NewInvalidLengthError(field string, minLength, maxLength int, value interface{}) *ValidationError {
	var message string
	if minLength > 0 && maxLength > 0 {
		message = fmt.Sprintf("%s must be between %d and %d characters long", field, minLength, maxLength)
	} else if minLength > 0 {
		message = fmt.Sprintf("%s must be at least %d characters long", field, minLength)
	} else if maxLength > 0 {
		message = fmt.Sprintf("%s must be at most %d characters long", field, maxLength)
	} else {
		message = fmt.Sprintf("%s has invalid length", field)
	}
	return NewSingleFieldValidationError(field, message, value)
}

// NewInvalidValueError creates a validation error for invalid value
func NewInvalidValueError(field string, allowedValues []string, value interface{}) *ValidationError {
	message := fmt.Sprintf("%s must be one of: %s", field, strings.Join(allowedValues, ", "))
	return NewSingleFieldValidationError(field, message, value)
}

// NewInvalidRangeError creates a validation error for values outside allowed range
func NewInvalidRangeError(field string, min, max interface{}, value interface{}) *ValidationError {
	message := fmt.Sprintf("%s must be between %v and %v", field, min, max)
	return NewSingleFieldValidationError(field, message, value)
}
