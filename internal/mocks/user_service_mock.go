package mocks

import (
	"context"
	"fmt"
	"time"

	"github.com/eldon111/impact-resume-service/internal/database"
	"github.com/eldon111/impact-resume-service/internal/models"
	"github.com/eldon111/impact-resume-service/internal/services"
)

// MockUserService implements services.UserServiceInterface for testing
type MockUserService struct {
	shouldError bool
}

// NewMockUserService creates a new mock user service
func NewMockUserService() *MockUserService {
	return &MockUserService{shouldError: false}
}

// CreateOrUpdateOAuthUser mocks OAuth user creation/update
func (m *MockUserService) CreateOrUpdateOAuthUser(ctx context.Context, provider string, oauthData interface{}) (*models.User, error) {
	if m.shouldError {
		return nil, fmt.Errorf("failed to create/update OAuth user")
	}

	user := &models.User{
		ID:        1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	switch provider {
	case "github":
		if githubOAuth, ok := oauthData.(*models.GitHubOAuth); ok {
			githubOAuth.UserID = 1
			githubOAuth.CreatedAt = time.Now()
			githubOAuth.UpdatedAt = time.Now()
			user.GitHubOAuth = githubOAuth
		}
	case "google":
		if googleOAuth, ok := oauthData.(*models.GoogleOAuth); ok {
			googleOAuth.UserID = 1
			googleOAuth.CreatedAt = time.Now()
			googleOAuth.UpdatedAt = time.Now()
			user.GoogleOAuth = googleOAuth
		}
	case "linkedin":
		if linkedinOAuth, ok := oauthData.(*models.LinkedInOAuth); ok {
			linkedinOAuth.UserID = 1
			linkedinOAuth.CreatedAt = time.Now()
			linkedinOAuth.UpdatedAt = time.Now()
			user.LinkedInOAuth = linkedinOAuth
		}
	}

	return user, nil
}

// GetUserByID mocks user retrieval by ID
func (m *MockUserService) GetUserByID(ctx context.Context, userID int) (*models.User, error) {
	if m.shouldError {
		return nil, database.ErrUserNotFound
	}

	return &models.User{
		ID:        userID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}, nil
}

// Legacy methods for backward compatibility

// CreateOrUpdateGitHubUser mocks GitHub user creation/update
func (m *MockUserService) CreateOrUpdateGitHubUser(ctx context.Context, githubOAuth *models.GitHubOAuth) (*models.User, error) {
	return m.CreateOrUpdateOAuthUser(ctx, "github", githubOAuth)
}

// CreateOrUpdateGoogleUser mocks Google user creation/update
func (m *MockUserService) CreateOrUpdateGoogleUser(ctx context.Context, googleOAuth *models.GoogleOAuth) (*models.User, error) {
	return m.CreateOrUpdateOAuthUser(ctx, "google", googleOAuth)
}

// CreateOrUpdateLinkedInUser mocks LinkedIn user creation/update
func (m *MockUserService) CreateOrUpdateLinkedInUser(ctx context.Context, linkedinOAuth *models.LinkedInOAuth) (*models.User, error) {
	return m.CreateOrUpdateOAuthUser(ctx, "linkedin", linkedinOAuth)
}

// SetError configures the mock to return errors
func (m *MockUserService) SetError(shouldError bool) {
	m.shouldError = shouldError
}

// Ensure MockUserService implements the interface
var _ services.UserServiceInterface = (*MockUserService)(nil)
