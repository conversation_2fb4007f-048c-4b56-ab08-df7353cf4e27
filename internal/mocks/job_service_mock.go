package mocks

import (
	"context"
	"time"

	apperrors "github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
	"github.com/eldon111/impact-resume-service/internal/services"
)

// MockJobService implements services.JobServiceInterface for testing
type MockJobService struct {
	shouldError bool
}

// NewMockJobService creates a new mock job service
func NewMockJobService() *MockJobService {
	return &MockJobService{shouldError: false}
}

// CreateJob mocks job creation with validation
func (m *MockJobService) CreateJob(ctx context.Context, userID int, request *models.JobCreateRequest) (*models.Job, error) {
	if m.shouldError {
		return nil, apperrors.NewJobNotFoundError("mock", 0)
	}

	// Perform basic validation to match expected test behavior
	if request.Title == "" {
		return nil, apperrors.NewInvalidInputError("title", request.Title).WithDetails("Title is required")
	}
	if request.Company == "" {
		return nil, apperrors.NewInvalidInputError("company", request.Company).WithDetails("Company is required")
	}
	if request.Description == "" {
		return nil, apperrors.NewInvalidInputError("description", request.Description).WithDetails("Description is required")
	}
	if len(request.Title) > 200 {
		return nil, apperrors.NewInvalidInputError("title", request.Title).WithDetails("Title too long")
	}

	return &models.Job{
		ID:          "job_123",
		UserID:      userID,
		Title:       request.Title,
		Company:     request.Company,
		Location:    request.Location,
		StartDate:   request.StartDate,
		EndDate:     request.EndDate,
		IsCurrent:   request.IsCurrent,
		Description: request.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}, nil
}

// GetJob mocks job retrieval
func (m *MockJobService) GetJob(ctx context.Context, userID int, jobID string) (*models.Job, error) {
	if m.shouldError {
		return nil, apperrors.NewJobNotFoundError("mock", 0)
	}
	return &models.Job{
		ID:          jobID,
		UserID:      userID,
		Title:       "Software Engineer",
		Company:     "Tech Corp",
		Location:    "San Francisco, CA",
		Description: "Develop software applications",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}, nil
}

// GetJobs mocks job listing
func (m *MockJobService) GetJobs(ctx context.Context, userID int) ([]models.Job, error) {
	if m.shouldError {
		return nil, apperrors.NewJobNotFoundError("mock", 0)
	}
	return []models.Job{
		{
			ID:          "job_1",
			UserID:      userID,
			Title:       "Software Engineer",
			Company:     "Tech Corp",
			Description: "Develop software",
		},
		{
			ID:          "job_2",
			UserID:      userID,
			Title:       "Senior Developer",
			Company:     "Another Corp",
			Description: "Lead development",
		},
	}, nil
}

// UpdateJobDescription mocks job description updates
func (m *MockJobService) UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error {
	if m.shouldError {
		return apperrors.NewJobNotFoundError("mock", 0)
	}

	// Return not found for specific test job IDs
	if jobID == "nonexistent_job" {
		return apperrors.NewJobNotFoundError("mock", 0)
	}

	// Perform basic validation to match expected test behavior
	if description == "" {
		return apperrors.NewInvalidInputError("description", description).WithDetails("Description is required")
	}
	if len(description) > 5000 {
		return apperrors.NewInvalidInputError("description", description).WithDetails("Description too long")
	}

	return nil
}

// DeleteJob mocks job deletion
func (m *MockJobService) DeleteJob(ctx context.Context, userID int, jobID string) error {
	if m.shouldError {
		return apperrors.NewJobNotFoundError("mock", 0)
	}
	return nil
}

// ValidateJobRequest mocks job request validation
func (m *MockJobService) ValidateJobRequest(request *models.JobCreateRequest) error {
	if m.shouldError {
		return apperrors.NewValidationError("Mock validation failed")
	}
	return nil
}

// SetError configures the mock to return errors
func (m *MockJobService) SetError(shouldError bool) {
	m.shouldError = shouldError
}

// Ensure MockJobService implements the interface
var _ services.JobServiceInterface = (*MockJobService)(nil)
