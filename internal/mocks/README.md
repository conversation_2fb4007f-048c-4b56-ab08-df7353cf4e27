# Mocks Package

This package contains mock implementations of service interfaces for testing purposes. Following Go best practices, all mock services are centralized here to promote reusability across different test files.

## Available Mocks

### MockJobService
Implements `services.JobServiceInterface` for testing job-related operations.

**Usage:**
```go
import "github.com/eldon111/impactresume/internal/mocks"

func TestSomething(t *testing.T) {
    mockJobService := mocks.NewMockJobService()
    
    // Configure mock to return errors
    mockJobService.SetError(true)
    
    // Use in your tests...
}
```

**Features:**
- Basic validation logic that matches production behavior
- Configurable error responses via `SetError(bool)`
- Handles special test cases (e.g., "nonexistent_job" returns not found)

### MockProcessingService
Implements `services.ProcessingServiceInterface` for testing job processing workflows.

**Usage:**
```go
mockProcessingService := mocks.NewMockProcessingService()
mockProcessingService.SetError(false) // Configure behavior
```

**Features:**
- Simulates AI processing with mock bullet points
- Validates job ID lists and handles empty arrays
- Returns consistent mock data for testing

### MockAuthService
Implements `services.AuthServiceInterface` for testing authentication workflows.

**Usage:**
```go
mockAuthService := mocks.NewMockAuthService()
// Test OAuth callbacks, token generation, etc.
```

**Features:**
- Supports all OAuth providers (GitHub, Google, LinkedIn)
- Generates mock JWT tokens
- Handles token refresh scenarios

### MockUserService
Implements `services.UserServiceInterface` for testing user operations.

**Usage:**
```go
mockUserService := mocks.NewMockUserService()
// Test user creation, OAuth user handling, etc.
```

**Features:**
- Generic OAuth user creation/update
- Backward compatibility with legacy provider-specific methods
- Configurable error responses

## Design Principles

1. **Interface Compliance**: All mocks implement their respective service interfaces with compile-time verification
2. **Reusability**: Centralized location allows sharing across multiple test files
3. **Configurability**: `SetError()` methods allow testing both success and failure scenarios
4. **Realistic Behavior**: Mocks include basic validation to match production service behavior
5. **Test-Specific Handling**: Special cases like "nonexistent_job" for testing not-found scenarios

## Adding New Mocks

When adding new service interfaces:

1. Create a new file: `internal/mocks/{service_name}_mock.go`
2. Implement the service interface
3. Add `SetError(bool)` method for configurable behavior
4. Include interface compliance check: `var _ services.ServiceInterface = (*MockService)(nil)`
5. Update this README with usage examples

## Best Practices

- Always use the centralized mocks instead of creating inline test doubles
- Configure mock behavior explicitly in each test
- Reset mock state between tests if needed
- Use descriptive test data that makes test intentions clear
