package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/eldon111/impact-resume-service/internal/database"
	apperrors "github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
)

// UserServiceInterface defines the interface for user operations
type UserServiceInterface interface {
	CreateOrUpdateOAuthUser(ctx context.Context, provider string, oauthData interface{}) (*models.User, error)
	GetUserByID(ctx context.Context, userID int) (*models.User, error)
	// Legacy methods - to be deprecated
	CreateOrUpdateGitHubUser(ctx context.Context, githubOAuth *models.GitHubOAuth) (*models.User, error)
	CreateOrUpdateGoogleUser(ctx context.Context, googleOAuth *models.GoogleOAuth) (*models.User, error)
	CreateOrUpdateLinkedInUser(ctx context.Context, linkedinOAuth *models.LinkedInOAuth) (*models.User, error)
}

type UserService struct {
	userRepo database.UserRepository
}

func NewUserService(userRepo database.UserRepository) *UserService {
	return &UserService{
		userRepo: userRepo,
	}
}

// CreateOrUpdateOAuthUser creates or updates a user with OAuth data for any provider
func (us *UserService) CreateOrUpdateOAuthUser(ctx context.Context, provider string, oauthData interface{}) (*models.User, error) {
	switch provider {
	case "github":
		githubOAuth, ok := oauthData.(*models.GitHubOAuth)
		if !ok {
			return nil, fmt.Errorf("invalid GitHub OAuth data type")
		}
		return us.CreateOrUpdateGitHubUser(ctx, githubOAuth)
	case "google":
		googleOAuth, ok := oauthData.(*models.GoogleOAuth)
		if !ok {
			return nil, fmt.Errorf("invalid Google OAuth data type")
		}
		return us.CreateOrUpdateGoogleUser(ctx, googleOAuth)
	case "linkedin":
		linkedinOAuth, ok := oauthData.(*models.LinkedInOAuth)
		if !ok {
			return nil, fmt.Errorf("invalid LinkedIn OAuth data type")
		}
		return us.CreateOrUpdateLinkedInUser(ctx, linkedinOAuth)
	default:
		return nil, apperrors.NewInvalidInputError("provider", provider).
			WithDetails(fmt.Sprintf("Supported providers: github, google, linkedin"))
	}
}

func (us *UserService) CreateOrUpdateGitHubUser(ctx context.Context, githubOAuth *models.GitHubOAuth) (*models.User, error) {
	// First, try to find existing user with this GitHub ID
	existingUser, err := us.userRepo.GetUserByGitHubID(ctx, githubOAuth.GitHubID)

	if err == nil {
		// User exists, update GitHub OAuth data
		now := time.Now()
		githubOAuth.UserID = existingUser.ID
		githubOAuth.UpdatedAt = now
		githubOAuth.CreatedAt = existingUser.GitHubOAuth.CreatedAt // Preserve original creation time

		existingUser.GitHubOAuth = githubOAuth
		existingUser.UpdatedAt = now

		// Update the user document
		err = us.userRepo.UpdateUser(ctx, existingUser)
		if err != nil {
			return nil, err // Repository already returns proper error types
		}

		return existingUser, nil
	} else {
		// Check if it's a user not found error (expected) or a different error
		var userNotFoundErr *apperrors.AppError
		if !errors.As(err, &userNotFoundErr) || userNotFoundErr.Code != apperrors.ErrCodeUserNotFound {
			return nil, err // Unexpected error, pass it through
		}
	}

	// User doesn't exist, create new one
	userID, err := us.userRepo.GenerateUserID(ctx)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	now := time.Now()
	githubOAuth.UserID = userID
	githubOAuth.CreatedAt = now
	githubOAuth.UpdatedAt = now

	user := &models.User{
		ID:          userID,
		CreatedAt:   now,
		UpdatedAt:   now,
		GitHubOAuth: githubOAuth,
	}

	err = us.userRepo.CreateUser(ctx, user)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	return user, nil
}

func (us *UserService) CreateOrUpdateGoogleUser(ctx context.Context, googleOAuth *models.GoogleOAuth) (*models.User, error) {
	// First, try to find existing user with this Google ID
	existingUser, err := us.userRepo.GetUserByGoogleID(ctx, googleOAuth.GoogleID)

	if err == nil {
		// User exists, update Google OAuth data
		now := time.Now()
		googleOAuth.UserID = existingUser.ID
		googleOAuth.UpdatedAt = now
		googleOAuth.CreatedAt = existingUser.GoogleOAuth.CreatedAt // Preserve original creation time

		existingUser.GoogleOAuth = googleOAuth
		existingUser.UpdatedAt = now

		// Update the user document
		err = us.userRepo.UpdateUser(ctx, existingUser)
		if err != nil {
			return nil, err // Repository already returns proper error types
		}

		return existingUser, nil
	} else {
		// Check if it's a user not found error (expected) or a different error
		var userNotFoundErr *apperrors.AppError
		if !errors.As(err, &userNotFoundErr) || userNotFoundErr.Code != apperrors.ErrCodeUserNotFound {
			return nil, err // Unexpected error, pass it through
		}
	}

	// User doesn't exist, create new one
	userID, err := us.userRepo.GenerateUserID(ctx)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	now := time.Now()
	googleOAuth.UserID = userID
	googleOAuth.CreatedAt = now
	googleOAuth.UpdatedAt = now

	user := &models.User{
		ID:          userID,
		CreatedAt:   now,
		UpdatedAt:   now,
		GoogleOAuth: googleOAuth,
	}

	err = us.userRepo.CreateUser(ctx, user)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	return user, nil
}

func (us *UserService) CreateOrUpdateLinkedInUser(ctx context.Context, linkedinOAuth *models.LinkedInOAuth) (*models.User, error) {
	// First, try to find existing user with this LinkedIn ID
	existingUser, err := us.userRepo.GetUserByLinkedInID(ctx, linkedinOAuth.LinkedInID)

	if err == nil {
		// User exists, update LinkedIn OAuth data
		now := time.Now()
		linkedinOAuth.UserID = existingUser.ID
		linkedinOAuth.UpdatedAt = now
		linkedinOAuth.CreatedAt = existingUser.LinkedInOAuth.CreatedAt // Preserve original creation time

		existingUser.LinkedInOAuth = linkedinOAuth
		existingUser.UpdatedAt = now

		// Update the user document
		err = us.userRepo.UpdateUser(ctx, existingUser)
		if err != nil {
			return nil, err // Repository already returns proper error types
		}

		return existingUser, nil
	} else {
		// Check if it's a user not found error (expected) or a different error
		var userNotFoundErr *apperrors.AppError
		if !errors.As(err, &userNotFoundErr) || userNotFoundErr.Code != apperrors.ErrCodeUserNotFound {
			return nil, err // Unexpected error, pass it through
		}
	}

	// User doesn't exist, create new one
	userID, err := us.userRepo.GenerateUserID(ctx)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	now := time.Now()
	linkedinOAuth.UserID = userID
	linkedinOAuth.CreatedAt = now
	linkedinOAuth.UpdatedAt = now

	user := &models.User{
		ID:            userID,
		CreatedAt:     now,
		UpdatedAt:     now,
		LinkedInOAuth: linkedinOAuth,
	}

	err = us.userRepo.CreateUser(ctx, user)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	return user, nil
}

func (us *UserService) GetUserByID(ctx context.Context, userID int) (*models.User, error) {
	user, err := us.userRepo.GetUserByID(ctx, userID)
	if err != nil {
		// The repository already returns proper error types, so we can pass them through
		return nil, err
	}

	return user, nil
}
