package services

import (
	"context"
	"fmt"
	"time"

	"github.com/eldon111/impact-resume-service/internal/models"
)

// ProcessingServiceInterface defines the interface for job processing operations
type ProcessingServiceInterface interface {
	ProcessSingleJob(ctx context.Context, userID int, jobID string) (*models.ProcessedJob, error)
	ProcessMultipleJobs(ctx context.Context, userID int, jobIDs []string) ([]models.ProcessedJob, error)
	CreateProcessedJob(ctx context.Context, job *models.Job, result *JobCondensationResult) (*models.ProcessedJob, error)
	ValidateProcessingRequest(request *models.JobProcessRequest) error
}

// ProcessingService handles job processing workflows
type ProcessingService struct {
	jobService JobServiceInterface
	aiService  AIServiceInterface
	validator  *ValidationService
}

// NewProcessingService creates a new processing service instance
func NewProcessingService(jobService JobServiceInterface, aiService AIServiceInterface, validator *ValidationService) *ProcessingService {
	return &ProcessingService{
		jobService: jobService,
		aiService:  aiService,
		validator:  validator,
	}
}

// ProcessSingleJob processes a single job with AI and returns the result
func (ps *ProcessingService) ProcessSingleJob(ctx context.Context, userID int, jobID string) (*models.ProcessedJob, error) {
	// Get the job
	job, err := ps.jobService.GetJob(ctx, userID, jobID)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve job: %w", err)
	}

	// Process with AI
	result, err := ps.aiService.CondenseJobHistory(ctx, job.Title, job.Company, job.Description)
	if err != nil {
		return nil, fmt.Errorf("failed to process job with AI: %w", err)
	}

	// Create processed job
	processedJob, err := ps.CreateProcessedJob(ctx, job, result)
	if err != nil {
		return nil, fmt.Errorf("failed to create processed job: %w", err)
	}

	return processedJob, nil
}

// ProcessMultipleJobs processes multiple jobs with AI and returns the results
func (ps *ProcessingService) ProcessMultipleJobs(ctx context.Context, userID int, jobIDs []string) ([]models.ProcessedJob, error) {
	if len(jobIDs) == 0 {
		return nil, fmt.Errorf("job IDs list cannot be empty")
	}

	var processedJobs []models.ProcessedJob

	for _, jobID := range jobIDs {
		processedJob, err := ps.ProcessSingleJob(ctx, userID, jobID)
		if err != nil {
			return nil, fmt.Errorf("failed to process job %s: %w", jobID, err)
		}

		processedJobs = append(processedJobs, *processedJob)
	}

	return processedJobs, nil
}

// CreateProcessedJob creates a ProcessedJob from a Job and AI result
func (ps *ProcessingService) CreateProcessedJob(ctx context.Context, job *models.Job, result *JobCondensationResult) (*models.ProcessedJob, error) {
	if job == nil {
		return nil, fmt.Errorf("job cannot be nil")
	}
	if result == nil {
		return nil, fmt.Errorf("AI result cannot be nil")
	}

	processedJob := &models.ProcessedJob{
		JobID:        job.ID,
		BulletPoints: result.BulletPoints,
		Model:        result.Model,
		ProcessedAt:  time.Now(),
		TokensUsed:   result.TokensUsed,
	}

	return processedJob, nil
}

// ValidateProcessingRequest validates a job processing request
func (ps *ProcessingService) ValidateProcessingRequest(request *models.JobProcessRequest) error {
	if request == nil {
		return fmt.Errorf("request cannot be nil")
	}

	return ps.validator.ValidateStruct(request)
}
