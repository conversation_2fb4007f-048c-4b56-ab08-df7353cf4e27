package services

import (
	"fmt"
	"strings"

	apperrors "github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/go-playground/validator/v10"
)

type ValidationService struct {
	validator *validator.Validate
}

func NewValidationService() *ValidationService {
	validate := validator.New()

	// Register custom validators
	validate.RegisterValidation("date_range", validateDateRange)

	return &ValidationService{
		validator: validate,
	}
}

func (vs *ValidationService) ValidateStruct(s interface{}) error {
	err := vs.validator.Struct(s)
	if err != nil {
		return apperrors.NewValidationErrorFromValidator(err)
	}
	return nil
}

func (vs *ValidationService) FormatValidationErrors(err error) []string {
	var errorMessages []string

	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrors {
			errorMessages = append(errorMessages, vs.formatSingleError(e))
		}
	}

	return errorMessages
}

func (vs *ValidationService) formatSingleError(e validator.FieldError) string {
	field := strings.ToLower(e.Field())

	switch e.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", field)
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", field, e.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", field, e.Param())
	case "email":
		return fmt.Sprintf("%s must be a valid email address", field)
	case "date_range":
		return fmt.Sprintf("end date must be after start date")
	default:
		return fmt.Sprintf("%s is invalid", field)
	}
}

// Custom validation function for date ranges
func validateDateRange(fl validator.FieldLevel) bool {
	// This would need to be implemented based on your DateOnly type
	// For now, returning true as placeholder
	return true
}
