package services

import (
	"testing"

	"github.com/eldon111/impact-resume-service/internal/models"
)

func TestValidationService_ValidateStruct(t *testing.T) {
	vs := NewValidationService()

	tests := []struct {
		name      string
		input     interface{}
		wantError bool
	}{
		{
			name: "Valid JobCreateRequest",
			input: models.JobCreateRequest{
				Title:       "Software Engineer",
				Company:     "Tech Corp",
				Location:    "San Francisco, CA",
				StartDate:   models.DateOnly{},
				Description: "Developed software applications",
				IsCurrent:   true,
			},
			wantError: false,
		},
		{
			name: "Missing required title",
			input: models.JobCreateRequest{
				Company:     "Tech Corp",
				Location:    "San Francisco, CA",
				StartDate:   models.DateOnly{},
				Description: "Developed software applications",
				IsCurrent:   true,
			},
			wantError: true,
		},
		{
			name: "Missing required company",
			input: models.JobCreateRequest{
				Title:       "Software Engineer",
				Location:    "San Francisco, CA",
				StartDate:   models.DateOnly{},
				Description: "Developed software applications",
				IsCurrent:   true,
			},
			wantError: true,
		},
		{
			name: "Missing required description",
			input: models.JobCreateRequest{
				Title:     "Software Engineer",
				Company:   "Tech Corp",
				Location:  "San Francisco, CA",
				StartDate: models.DateOnly{},
				IsCurrent: true,
			},
			wantError: true,
		},
		{
			name: "Title too long",
			input: models.JobCreateRequest{
				Title:       "This is a very long title that exceeds the maximum allowed length of 200 characters which should cause a validation error when we try to validate this struct because the title field has a max validation tag that limits it to 200 characters maximum",
				Company:     "Tech Corp",
				Location:    "San Francisco, CA",
				StartDate:   models.DateOnly{},
				Description: "Developed software applications",
				IsCurrent:   true,
			},
			wantError: true,
		},
		{
			name: "Company too long",
			input: models.JobCreateRequest{
				Title:       "Software Engineer",
				Company:     "This is a very long company name that exceeds the maximum allowed length of 200 characters which should cause a validation error when we try to validate this struct because the company field has a max validation tag that limits it to 200 characters maximum",
				Location:    "San Francisco, CA",
				StartDate:   models.DateOnly{},
				Description: "Developed software applications",
				IsCurrent:   true,
			},
			wantError: true,
		},
		{
			name: "Description too long",
			input: models.JobCreateRequest{
				Title:     "Software Engineer",
				Company:   "Tech Corp",
				Location:  "San Francisco, CA",
				StartDate: models.DateOnly{},
				Description: func() string {
					// Create a description longer than 5000 characters
					desc := ""
					for i := 0; i < 5001; i++ {
						desc += "a"
					}
					return desc
				}(),
				IsCurrent: true,
			},
			wantError: true,
		},
		{
			name: "Valid JobProcessRequest",
			input: models.JobProcessRequest{
				JobIDs: []string{"job_123", "job_456"},
				Model:  "claude-3-haiku",
			},
			wantError: false,
		},
		{
			name: "Empty JobIDs array",
			input: models.JobProcessRequest{
				JobIDs: []string{},
				Model:  "claude-3-haiku",
			},
			wantError: true,
		},
		{
			name: "Nil JobIDs array",
			input: models.JobProcessRequest{
				Model: "claude-3-haiku",
			},
			wantError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := vs.ValidateStruct(tt.input)

			if tt.wantError {
				if err == nil {
					t.Errorf("Expected validation error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected validation error: %v", err)
				}
			}
		})
	}
}

func TestValidationService_FormatValidationErrors(t *testing.T) {
	vs := NewValidationService()

	tests := []struct {
		name     string
		input    interface{}
		expected []string
	}{
		{
			name: "Missing required fields",
			input: models.JobCreateRequest{
				Location:  "San Francisco, CA",
				IsCurrent: true,
			},
			expected: []string{
				"title is required",
				"company is required",
				"description is required",
			},
		},
		{
			name: "Field too long",
			input: models.JobCreateRequest{
				Title:       "This is a very long title that exceeds the maximum allowed length of 200 characters which should cause a validation error when we try to validate this struct because the title field has a max validation tag that limits it to 200 characters maximum",
				Company:     "Tech Corp",
				StartDate:   models.DateOnly{},
				Description: "Valid description",
				IsCurrent:   true,
			},
			expected: []string{
				"title must be at most 200 characters long",
			},
		},
		{
			name: "Empty JobIDs array",
			input: models.JobProcessRequest{
				JobIDs: []string{},
				Model:  "claude-3-haiku",
			},
			expected: []string{
				"jobids must be at least 1 characters long",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := vs.ValidateStruct(tt.input)
			if err == nil {
				t.Fatalf("Expected validation error but got none")
			}

			errors := vs.FormatValidationErrors(err)

			if len(errors) == 0 {
				t.Errorf("Expected formatted errors but got none")
				return
			}

			// Check that we got the expected number of errors
			if len(errors) != len(tt.expected) {
				t.Errorf("Expected %d errors, got %d: %v", len(tt.expected), len(errors), errors)
			}

			// Check that each expected error message is present
			for _, expectedError := range tt.expected {
				found := false
				for _, actualError := range errors {
					if actualError == expectedError {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("Expected error '%s' not found in: %v", expectedError, errors)
				}
			}
		})
	}
}

func TestValidationService_formatSingleError(t *testing.T) {
	vs := NewValidationService()

	// Test with a simple struct to generate specific validation errors
	type TestStruct struct {
		Name  string `validate:"required,min=2,max=10"`
		Email string `validate:"email"`
	}

	tests := []struct {
		name      string
		input     TestStruct
		expectErr string
	}{
		{
			name:      "Required field missing",
			input:     TestStruct{Email: "<EMAIL>"},
			expectErr: "name is required",
		},
		{
			name:      "Field too short",
			input:     TestStruct{Name: "a", Email: "<EMAIL>"},
			expectErr: "name must be at least 2 characters long",
		},
		{
			name:      "Field too long",
			input:     TestStruct{Name: "verylongname", Email: "<EMAIL>"},
			expectErr: "name must be at most 10 characters long",
		},
		{
			name:      "Invalid email",
			input:     TestStruct{Name: "John", Email: "invalid-email"},
			expectErr: "email must be a valid email address",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := vs.ValidateStruct(tt.input)
			if err == nil {
				t.Fatalf("Expected validation error but got none")
			}

			errors := vs.FormatValidationErrors(err)
			if len(errors) == 0 {
				t.Errorf("Expected formatted errors but got none")
				return
			}

			// Check that the expected error message is present
			found := false
			for _, actualError := range errors {
				if actualError == tt.expectErr {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected error '%s' not found in: %v", tt.expectErr, errors)
			}
		})
	}
}
