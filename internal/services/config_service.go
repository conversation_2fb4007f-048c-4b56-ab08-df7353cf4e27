package services

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/joho/godotenv"
	"gopkg.in/yaml.v3"

	appconfig "github.com/eldon111/impact-resume-service/internal/config"
)

// Environment represents the application environment
type Environment string

const (
	EnvLocal       Environment = "local" // Local development - uses config file with direct API key
	EnvDevelopment Environment = "dev"   // Development environment - uses AWS Secrets Manager
	EnvProduction  Environment = "prod"  // Production environment - uses AWS Secrets Manager
)

// Config represents the overall application configuration structure
type Config struct {
	Claude   ClaudeConfig   `yaml:"claude"`
	Database DatabaseConfig `yaml:"database"`
	Auth     AuthConfig     `yaml:"auth"`
}

// ClaudeConfig contains configuration for Claude API integration
type ClaudeConfig struct {
	// APIKey is the direct API key used in local development only
	// This field is ignored in cloud environments for security
	APIKey string `yaml:"api_key,omitempty"`

	// SecretName is the name of the AWS Secrets Manager secret containing the API key
	// Used in development and production environments
	// Defaults to "claude-api-key" if not specified
	SecretName string `yaml:"secret_name,omitempty"`
}

// DatabaseConfig contains database configuration
type DatabaseConfig struct {
	URI      string `yaml:"uri"`
	Database string `yaml:"database"`
}

// AuthConfig contains authentication configuration for AWS Secrets Manager
type AuthConfig struct {
	// SecretName is the name of the AWS Secrets Manager secret containing auth credentials
	// Used in development and production environments
	// Defaults to "auth-secrets" if not specified
	SecretName string `yaml:"secret_name,omitempty"`
}

// ConfigService manages application configuration based on environment
type ConfigService struct {
	env           Environment
	config        *Config
	secretsClient *secretsmanager.Client
}

// NewConfigService creates a new configuration service instance
// It automatically detects the environment from APP_ENV environment variable
// and initializes the appropriate configuration source (file or AWS Secrets Manager)
func NewConfigService(ctx context.Context) (*ConfigService, error) {
	env := Environment(os.Getenv("APP_ENV"))
	if env == "" {
		env = EnvLocal
	}

	cs := &ConfigService{
		env: env,
	}

	if env == EnvLocal {
		// Load .env file for local development (optional, won't fail if not found)
		cs.loadEnvFile()

		// Load configuration from YAML file for local development
		if err := cs.loadConfigFile(); err != nil {
			return nil, fmt.Errorf("failed to load config file: %w", err)
		}
	} else {
		// Initialize AWS Secrets Manager client for cloud environments
		cfg, err := config.LoadDefaultConfig(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to load AWS config: %w", err)
		}
		cs.secretsClient = secretsmanager.NewFromConfig(cfg)
	}

	return cs, nil
}

// loadEnvFile loads environment variables from .env files for local development
// Tries to load .env.local first, then .env if .env.local doesn't exist
// This method doesn't return an error if files don't exist, as .env files are optional
func (cs *ConfigService) loadEnvFile() {
	// Try to load .env.local first (for local overrides)
	if err := godotenv.Load(".env.local"); err == nil {
		return // Successfully loaded .env.local
	}

	// If .env.local doesn't exist, try .env
	if err := godotenv.Load(".env"); err == nil {
		return // Successfully loaded .env
	}

	// If neither file exists, that's fine - environment variables can be set manually
}

// loadConfigFile loads configuration from the environment-specific YAML file
// Files are located in configs/{environment}.yaml
func (cs *ConfigService) loadConfigFile() error {
	configPath := filepath.Join("configs", fmt.Sprintf("%s.yaml", cs.env))

	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file %s: %w", configPath, err)
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}

	cs.config = &cfg
	return nil
}

// GetClaudeAPIKey retrieves the Claude API key based on the current environment
// - Local: Returns the API key directly from the config file
// - Cloud: Retrieves the API key from AWS Secrets Manager using the configured secret name
func (cs *ConfigService) GetClaudeAPIKey(ctx context.Context) (string, error) {
	if cs.env == EnvLocal {
		// For local development, use the API key from the config file
		if cs.config == nil {
			return "", fmt.Errorf("config not loaded")
		}
		if cs.config.Claude.APIKey == "" {
			return "", fmt.Errorf("claude API key not found in config file")
		}
		return cs.config.Claude.APIKey, nil
	}

	// For cloud environments, retrieve from AWS Secrets Manager
	secretName := "claude-api-key"
	if cs.config != nil && cs.config.Claude.SecretName != "" {
		secretName = cs.config.Claude.SecretName
	}

	result, err := cs.secretsClient.GetSecretValue(ctx, &secretsmanager.GetSecretValueInput{
		SecretId: &secretName,
	})
	if err != nil {
		return "", fmt.Errorf("failed to retrieve secret from AWS Secrets Manager: %w", err)
	}

	if result.SecretString == nil {
		return "", fmt.Errorf("secret value is nil")
	}

	return *result.SecretString, nil
}

// GetAuthSecrets retrieves authentication secrets based on the current environment
// - Local: Returns secrets from environment variables (maintains backward compatibility)
// - Cloud: Retrieves secrets from AWS Secrets Manager using the configured secret name
func (cs *ConfigService) GetAuthSecrets(ctx context.Context) (*appconfig.AuthSecrets, error) {
	if cs.env == EnvLocal {
		// For local development, use environment variables
		return &appconfig.AuthSecrets{
			JWTSecret:            os.Getenv("JWT_SECRET"),
			GitHubClientID:       os.Getenv("GITHUB_CLIENT_ID"),
			GitHubClientSecret:   os.Getenv("GITHUB_CLIENT_SECRET"),
			GoogleClientID:       os.Getenv("GOOGLE_CLIENT_ID"),
			GoogleClientSecret:   os.Getenv("GOOGLE_CLIENT_SECRET"),
			LinkedInClientID:     os.Getenv("LINKEDIN_CLIENT_ID"),
			LinkedInClientSecret: os.Getenv("LINKEDIN_CLIENT_SECRET"),
			Domain:               os.Getenv("DOMAIN"),
			FrontendURL:          os.Getenv("FRONTEND_URL"),
		}, nil
	}

	// For cloud environments, retrieve from AWS Secrets Manager
	secretName := "auth-secrets"
	if cs.config != nil && cs.config.Auth.SecretName != "" {
		secretName = cs.config.Auth.SecretName
	}

	result, err := cs.secretsClient.GetSecretValue(ctx, &secretsmanager.GetSecretValueInput{
		SecretId: &secretName,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve auth secrets from AWS Secrets Manager: %w", err)
	}

	if result.SecretString == nil {
		return nil, fmt.Errorf("auth secret value is nil")
	}

	var secrets appconfig.AuthSecrets
	if err := json.Unmarshal([]byte(*result.SecretString), &secrets); err != nil {
		return nil, fmt.Errorf("failed to parse auth secrets JSON: %w", err)
	}

	return &secrets, nil
}

// GetDatabaseConfig returns the database configuration
func (cs *ConfigService) GetDatabaseConfig() (*DatabaseConfig, error) {
	if cs.config == nil {
		return nil, fmt.Errorf("config not loaded")
	}

	if cs.config.Database.URI == "" {
		return nil, fmt.Errorf("database URI not found in config")
	}

	if cs.config.Database.Database == "" {
		return nil, fmt.Errorf("database name not found in config")
	}

	return &cs.config.Database, nil
}
