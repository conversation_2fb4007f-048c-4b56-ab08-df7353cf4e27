package database

import (
	"context"
	"errors"
	"testing"
	"time"

	apperrors "github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
)

func TestMockUserRepository_CreateUser(t *testing.T) {
	repo := NewMockUserRepository()
	ctx := context.Background()

	user := &models.User{
		ID:        1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		GitHubOAuth: &models.GitHubOAuth{
			GitHubID: "github123",
			Username: "testuser",
		},
	}

	err := repo.CreateUser(ctx, user)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Verify user was created
	retrievedUser, err := repo.GetUserByID(ctx, 1)
	if err != nil {
		t.Errorf("Expected no error when retrieving user, got %v", err)
	}

	if retrievedUser.ID != user.ID {
		t.<PERSON>rrorf("Expected user ID %d, got %d", user.ID, retrievedUser.ID)
	}
}

func TestMockUserRepository_GetUserByGitHubID(t *testing.T) {
	repo := NewMockUserRepository()
	ctx := context.Background()

	user := &models.User{
		ID:        1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		GitHubOAuth: &models.GitHubOAuth{
			GitHubID: "github123",
			Username: "testuser",
		},
	}

	err := repo.CreateUser(ctx, user)
	if err != nil {
		t.Errorf("Expected no error creating user, got %v", err)
	}

	// Test successful retrieval
	retrievedUser, err := repo.GetUserByGitHubID(ctx, "github123")
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if retrievedUser.ID != user.ID {
		t.Errorf("Expected user ID %d, got %d", user.ID, retrievedUser.ID)
	}

	// Test user not found
	_, err = repo.GetUserByGitHubID(ctx, "nonexistent")
	var appErr *apperrors.AppError
	if !errors.As(err, &appErr) || appErr.Code != apperrors.ErrCodeUserNotFound {
		t.Errorf("Expected UserNotFound error, got %v", err)
	}
}

func TestMockUserRepository_UpdateUser(t *testing.T) {
	repo := NewMockUserRepository()
	ctx := context.Background()

	user := &models.User{
		ID:        1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		GitHubOAuth: &models.GitHubOAuth{
			GitHubID: "github123",
			Username: "testuser",
		},
	}

	err := repo.CreateUser(ctx, user)
	if err != nil {
		t.Errorf("Expected no error creating user, got %v", err)
	}

	// Update user
	user.GitHubOAuth.Username = "updateduser"
	err = repo.UpdateUser(ctx, user)
	if err != nil {
		t.Errorf("Expected no error updating user, got %v", err)
	}

	// Verify update
	retrievedUser, err := repo.GetUserByID(ctx, 1)
	if err != nil {
		t.Errorf("Expected no error retrieving user, got %v", err)
	}

	if retrievedUser.GitHubOAuth.Username != "updateduser" {
		t.Errorf("Expected username 'updateduser', got '%s'", retrievedUser.GitHubOAuth.Username)
	}
}

func TestMockUserRepository_GenerateUserID(t *testing.T) {
	repo := NewMockUserRepository()
	ctx := context.Background()

	// Test first ID generation
	id1, err := repo.GenerateUserID(ctx)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if id1 != 1 {
		t.Errorf("Expected first ID to be 1, got %d", id1)
	}

	// Test second ID generation
	id2, err := repo.GenerateUserID(ctx)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if id2 != 2 {
		t.Errorf("Expected second ID to be 2, got %d", id2)
	}
}

func TestMockUserRepository_ErrorHandling(t *testing.T) {
	repo := NewMockUserRepository()
	repo.SetShouldError(true)
	ctx := context.Background()

	user := &models.User{
		ID:        1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Test error on create
	err := repo.CreateUser(ctx, user)
	if err == nil {
		t.Error("Expected error, got nil")
	}

	// Test error on get
	_, err = repo.GetUserByID(ctx, 1)
	if err == nil {
		t.Error("Expected error, got nil")
	}

	// Test error on generate ID
	_, err = repo.GenerateUserID(ctx)
	if err == nil {
		t.Error("Expected error, got nil")
	}
}
