package database

import (
	"context"

	"go.mongodb.org/mongo-driver/mongo"
)

type Database interface {
	Collection(name string) Collection
	Close(ctx context.Context) error
}

type Collection interface {
	InsertOne(ctx context.Context, document interface{}) (*mongo.InsertOneResult, error)
	FindOne(ctx context.Context, filter interface{}) *mongo.SingleResult
	Find(ctx context.Context, filter interface{}) (*mongo.Cursor, error)
	UpdateOne(ctx context.Context, filter interface{}, update interface{}) (*mongo.UpdateResult, error)
	DeleteOne(ctx context.Context, filter interface{}) (*mongo.DeleteResult, error)
	Aggregate(ctx context.Context, pipeline interface{}) (*mongo.Cursor, error)
}

type MongoDB struct {
	client   *mongo.Client
	database *mongo.Database
}

func NewMongoDB(client *mongo.Client, dbName string) *MongoDB {
	return &MongoDB{
		client:   client,
		database: client.Database(dbName),
	}
}

func (m *MongoDB) Collection(name string) Collection {
	return &MongoCollection{
		collection: m.database.Collection(name),
	}
}

func (m *MongoDB) Close(ctx context.Context) error {
	return m.client.Disconnect(ctx)
}

type MongoCollection struct {
	collection *mongo.Collection
}

func (c *MongoCollection) InsertOne(ctx context.Context, document interface{}) (*mongo.InsertOneResult, error) {
	return c.collection.InsertOne(ctx, document)
}

func (c *MongoCollection) FindOne(ctx context.Context, filter interface{}) *mongo.SingleResult {
	return c.collection.FindOne(ctx, filter)
}

func (c *MongoCollection) Find(ctx context.Context, filter interface{}) (*mongo.Cursor, error) {
	return c.collection.Find(ctx, filter)
}

func (c *MongoCollection) UpdateOne(ctx context.Context, filter interface{}, update interface{}) (*mongo.UpdateResult, error) {
	return c.collection.UpdateOne(ctx, filter, update)
}

func (c *MongoCollection) DeleteOne(ctx context.Context, filter interface{}) (*mongo.DeleteResult, error) {
	return c.collection.DeleteOne(ctx, filter)
}

func (c *MongoCollection) Aggregate(ctx context.Context, pipeline interface{}) (*mongo.Cursor, error) {
	return c.collection.Aggregate(ctx, pipeline)
}
