package database

import (
	"context"
	"errors"
	"testing"
	"time"

	apperrors "github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
)

func TestMockJobRepository_CreateJob(t *testing.T) {
	repo := NewMockJobRepository()
	ctx := context.Background()

	job := &models.Job{
		ID:          "job123",
		UserID:      1,
		Title:       "Software Engineer",
		Company:     "Test Company",
		Location:    "Remote",
		Description: "Test job description",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err := repo.CreateJob(ctx, job)
	if err != nil {
		t.<PERSON><PERSON>rf("Expected no error, got %v", err)
	}

	// Verify job was created
	retrievedJob, err := repo.GetJobByID(ctx, 1, "job123")
	if err != nil {
		t.<PERSON><PERSON>rf("Expected no error when retrieving job, got %v", err)
	}

	if retrievedJob.ID != job.ID {
		t.<PERSON><PERSON><PERSON>("Expected job ID %s, got %s", job.ID, retrievedJob.ID)
	}

	if retrievedJob.Title != job.Title {
		t.<PERSON><PERSON>rf("Expected job title %s, got %s", job.Title, retrievedJob.Title)
	}
}

func TestMockJobRepository_GetJobByID(t *testing.T) {
	repo := NewMockJobRepository()
	ctx := context.Background()

	job := &models.Job{
		ID:          "job123",
		UserID:      1,
		Title:       "Software Engineer",
		Company:     "Test Company",
		Location:    "Remote",
		Description: "Test job description",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err := repo.CreateJob(ctx, job)
	if err != nil {
		t.Errorf("Expected no error creating job, got %v", err)
	}

	// Test successful retrieval
	retrievedJob, err := repo.GetJobByID(ctx, 1, "job123")
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if retrievedJob.ID != job.ID {
		t.Errorf("Expected job ID %s, got %s", job.ID, retrievedJob.ID)
	}

	// Test job not found
	_, err = repo.GetJobByID(ctx, 1, "nonexistent")
	var appErr *apperrors.AppError
	if !errors.As(err, &appErr) || appErr.Code != apperrors.ErrCodeJobNotFound {
		t.Errorf("Expected JobNotFound error, got %v", err)
	}

	// Test wrong user ID
	_, err = repo.GetJobByID(ctx, 2, "job123")
	if !errors.As(err, &appErr) || appErr.Code != apperrors.ErrCodeJobNotFound {
		t.Errorf("Expected JobNotFound error for wrong user ID, got %v", err)
	}
}

func TestMockJobRepository_GetJobs(t *testing.T) {
	repo := NewMockJobRepository()
	ctx := context.Background()

	// Create jobs for different users
	job1 := &models.Job{
		ID:        "job1",
		UserID:    1,
		Title:     "Software Engineer",
		Company:   "Company A",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	job2 := &models.Job{
		ID:        "job2",
		UserID:    1,
		Title:     "Senior Engineer",
		Company:   "Company B",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	job3 := &models.Job{
		ID:        "job3",
		UserID:    2,
		Title:     "Manager",
		Company:   "Company C",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	repo.CreateJob(ctx, job1)
	repo.CreateJob(ctx, job2)
	repo.CreateJob(ctx, job3)

	// Get jobs for user 1
	jobs, err := repo.GetJobs(ctx, 1)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(jobs) != 2 {
		t.Errorf("Expected 2 jobs for user 1, got %d", len(jobs))
	}

	// Get jobs for user 2
	jobs, err = repo.GetJobs(ctx, 2)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(jobs) != 1 {
		t.Errorf("Expected 1 job for user 2, got %d", len(jobs))
	}

	// Get jobs for non-existent user
	jobs, err = repo.GetJobs(ctx, 999)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(jobs) != 0 {
		t.Errorf("Expected 0 jobs for non-existent user, got %d", len(jobs))
	}
}

func TestMockJobRepository_UpdateJobDescription(t *testing.T) {
	repo := NewMockJobRepository()
	ctx := context.Background()

	job := &models.Job{
		ID:          "job123",
		UserID:      1,
		Title:       "Software Engineer",
		Company:     "Test Company",
		Description: "Original description",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err := repo.CreateJob(ctx, job)
	if err != nil {
		t.Errorf("Expected no error creating job, got %v", err)
	}

	// Update description
	newDescription := "Updated description"
	err = repo.UpdateJobDescription(ctx, 1, "job123", newDescription)
	if err != nil {
		t.Errorf("Expected no error updating job description, got %v", err)
	}

	// Verify update
	retrievedJob, err := repo.GetJobByID(ctx, 1, "job123")
	if err != nil {
		t.Errorf("Expected no error retrieving job, got %v", err)
	}

	if retrievedJob.Description != newDescription {
		t.Errorf("Expected description '%s', got '%s'", newDescription, retrievedJob.Description)
	}

	// Test update non-existent job
	err = repo.UpdateJobDescription(ctx, 1, "nonexistent", "new desc")
	var appErr *apperrors.AppError
	if !errors.As(err, &appErr) || appErr.Code != apperrors.ErrCodeJobNotFound {
		t.Errorf("Expected JobNotFound error, got %v", err)
	}
}

func TestMockJobRepository_DeleteJob(t *testing.T) {
	repo := NewMockJobRepository()
	ctx := context.Background()

	job := &models.Job{
		ID:        "job123",
		UserID:    1,
		Title:     "Software Engineer",
		Company:   "Test Company",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := repo.CreateJob(ctx, job)
	if err != nil {
		t.Errorf("Expected no error creating job, got %v", err)
	}

	// Delete job
	err = repo.DeleteJob(ctx, 1, "job123")
	if err != nil {
		t.Errorf("Expected no error deleting job, got %v", err)
	}

	// Verify job was deleted
	_, err = repo.GetJobByID(ctx, 1, "job123")
	var appErr *apperrors.AppError
	if !errors.As(err, &appErr) || appErr.Code != apperrors.ErrCodeJobNotFound {
		t.Errorf("Expected JobNotFound error after deletion, got %v", err)
	}

	// Test delete non-existent job
	err = repo.DeleteJob(ctx, 1, "nonexistent")
	if !errors.As(err, &appErr) || appErr.Code != apperrors.ErrCodeJobNotFound {
		t.Errorf("Expected JobNotFound error, got %v", err)
	}
}

func TestMockJobRepository_ErrorHandling(t *testing.T) {
	repo := NewMockJobRepository()
	repo.SetShouldError(true)
	ctx := context.Background()

	job := &models.Job{
		ID:        "job123",
		UserID:    1,
		Title:     "Software Engineer",
		Company:   "Test Company",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Test error on create
	err := repo.CreateJob(ctx, job)
	if err == nil {
		t.Error("Expected error, got nil")
	}

	// Test error on get
	_, err = repo.GetJobByID(ctx, 1, "job123")
	if err == nil {
		t.Error("Expected error, got nil")
	}

	// Test error on get jobs
	_, err = repo.GetJobs(ctx, 1)
	if err == nil {
		t.Error("Expected error, got nil")
	}
}
