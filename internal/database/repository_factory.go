package database

import (
	"context"

	"go.mongodb.org/mongo-driver/mongo"
)

// RepositoryFactory defines the interface for creating repository instances
type RepositoryFactory interface {
	// UserRepository returns a UserRepository instance
	UserRepository() UserRepository

	// JobRepository returns a JobRepository instance
	JobRepository() JobRepository

	// Close closes the database connection
	Close(ctx context.Context) error
}

// MongoRepositoryFactory implements RepositoryFactory for MongoDB
type MongoRepositoryFactory struct {
	client   *mongo.Client
	database *mongo.Database

	userRepo UserRepository
	jobRepo  JobRepository
}

// NewMongoRepositoryFactory creates a new MongoDB repository factory
func NewMongoRepositoryFactory(client *mongo.Client, dbName string) *MongoRepositoryFactory {
	database := client.Database(dbName)

	factory := &MongoRepositoryFactory{
		client:   client,
		database: database,
	}

	// Initialize repositories
	factory.userRepo = NewMongoUserRepository(database)
	factory.jobRepo = NewMongoJobRepository(database)

	return factory
}

// UserRepository returns a UserRepository instance
func (f *MongoRepositoryFactory) UserRepository() UserRepository {
	return f.userRepo
}

// JobRepository returns a JobRepository instance
func (f *MongoRepositoryFactory) JobRepository() JobRepository {
	return f.jobRepo
}

// Close closes the database connection
func (f *MongoRepositoryFactory) Close(ctx context.Context) error {
	return f.client.Disconnect(ctx)
}
