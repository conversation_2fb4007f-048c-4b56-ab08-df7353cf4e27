package database

import (
	"context"

	apperrors "github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
)

// MockUserRepository implements UserRepository for testing
type MockUserRepository struct {
	users           map[int]*models.User
	usersByGitHub   map[string]*models.User
	usersByGoogle   map[string]*models.User
	usersByLinkedIn map[string]*models.User
	shouldError     bool
	nextUserID      int
}

// NewMockUserRepository creates a new mock user repository
func NewMockUserRepository() *MockUserRepository {
	return &MockUserRepository{
		users:           make(map[int]*models.User),
		usersByGitHub:   make(map[string]*models.User),
		usersByGoogle:   make(map[string]*models.User),
		usersByLinkedIn: make(map[string]*models.User),
		shouldError:     false,
		nextUserID:      1,
	}
}

func (m *MockUserRepository) SetShouldError(shouldError bool) {
	m.shouldError = shouldError
}

func (m *MockUserRepository) CreateUser(ctx context.Context, user *models.User) error {
	if m.shouldError {
		return apperrors.NewDatabaseError("mock create user", nil)
	}
	m.users[user.ID] = user

	// Update lookup maps
	if user.GitHubOAuth != nil {
		m.usersByGitHub[user.GitHubOAuth.GitHubID] = user
	}
	if user.GoogleOAuth != nil {
		m.usersByGoogle[user.GoogleOAuth.GoogleID] = user
	}
	if user.LinkedInOAuth != nil {
		m.usersByLinkedIn[user.LinkedInOAuth.LinkedInID] = user
	}

	return nil
}

func (m *MockUserRepository) GetUserByID(ctx context.Context, userID int) (*models.User, error) {
	if m.shouldError {
		return nil, apperrors.NewDatabaseError("mock get user by ID", nil)
	}
	user, exists := m.users[userID]
	if !exists {
		return nil, apperrors.NewUserNotFoundError(userID)
	}
	return user, nil
}

func (m *MockUserRepository) GetUserByGitHubID(ctx context.Context, githubID string) (*models.User, error) {
	if m.shouldError {
		return nil, apperrors.NewDatabaseError("mock get user by GitHub ID", nil)
	}
	user, exists := m.usersByGitHub[githubID]
	if !exists {
		return nil, apperrors.NewUserNotFoundError(githubID)
	}
	return user, nil
}

func (m *MockUserRepository) GetUserByGoogleID(ctx context.Context, googleID string) (*models.User, error) {
	if m.shouldError {
		return nil, apperrors.NewDatabaseError("mock get user by Google ID", nil)
	}
	user, exists := m.usersByGoogle[googleID]
	if !exists {
		return nil, apperrors.NewUserNotFoundError(googleID)
	}
	return user, nil
}

func (m *MockUserRepository) GetUserByLinkedInID(ctx context.Context, linkedinID string) (*models.User, error) {
	if m.shouldError {
		return nil, apperrors.NewDatabaseError("mock get user by LinkedIn ID", nil)
	}
	user, exists := m.usersByLinkedIn[linkedinID]
	if !exists {
		return nil, apperrors.NewUserNotFoundError(linkedinID)
	}
	return user, nil
}

func (m *MockUserRepository) UpdateUser(ctx context.Context, user *models.User) error {
	if m.shouldError {
		return apperrors.NewDatabaseError("mock update user", nil)
	}
	_, exists := m.users[user.ID]
	if !exists {
		return apperrors.NewUserNotFoundError(user.ID)
	}
	m.users[user.ID] = user

	// Update lookup maps
	if user.GitHubOAuth != nil {
		m.usersByGitHub[user.GitHubOAuth.GitHubID] = user
	}
	if user.GoogleOAuth != nil {
		m.usersByGoogle[user.GoogleOAuth.GoogleID] = user
	}
	if user.LinkedInOAuth != nil {
		m.usersByLinkedIn[user.LinkedInOAuth.LinkedInID] = user
	}

	return nil
}

func (m *MockUserRepository) DeleteUser(ctx context.Context, userID int) error {
	if m.shouldError {
		return apperrors.NewDatabaseError("mock delete user", nil)
	}
	user, exists := m.users[userID]
	if !exists {
		return apperrors.NewUserNotFoundError(userID)
	}

	// Remove from lookup maps
	if user.GitHubOAuth != nil {
		delete(m.usersByGitHub, user.GitHubOAuth.GitHubID)
	}
	if user.GoogleOAuth != nil {
		delete(m.usersByGoogle, user.GoogleOAuth.GoogleID)
	}
	if user.LinkedInOAuth != nil {
		delete(m.usersByLinkedIn, user.LinkedInOAuth.LinkedInID)
	}

	delete(m.users, userID)
	return nil
}

func (m *MockUserRepository) GenerateUserID(ctx context.Context) (int, error) {
	if m.shouldError {
		return 0, apperrors.NewDatabaseError("mock generate user ID", nil)
	}
	id := m.nextUserID
	m.nextUserID++
	return id, nil
}

// MockJobRepository implements JobRepository for testing
type MockJobRepository struct {
	jobs        map[string]*models.Job
	shouldError bool
}

// NewMockJobRepository creates a new mock job repository
func NewMockJobRepository() *MockJobRepository {
	return &MockJobRepository{
		jobs:        make(map[string]*models.Job),
		shouldError: false,
	}
}

func (m *MockJobRepository) SetShouldError(shouldError bool) {
	m.shouldError = shouldError
}

func (m *MockJobRepository) CreateJob(ctx context.Context, job *models.Job) error {
	if m.shouldError {
		return apperrors.NewDatabaseError("mock create job", nil)
	}
	m.jobs[job.ID] = job
	return nil
}

func (m *MockJobRepository) GetJobByID(ctx context.Context, userID int, id string) (*models.Job, error) {
	if m.shouldError {
		return nil, apperrors.NewDatabaseError("mock get job by ID", nil)
	}
	job, exists := m.jobs[id]
	if !exists || job.UserID != userID {
		return nil, apperrors.NewJobNotFoundError(id, userID)
	}
	return job, nil
}

func (m *MockJobRepository) GetJobs(ctx context.Context, userID int) ([]models.Job, error) {
	if m.shouldError {
		return nil, apperrors.NewDatabaseError("mock get jobs", nil)
	}
	var jobs []models.Job
	for _, job := range m.jobs {
		if job.UserID == userID {
			jobs = append(jobs, *job)
		}
	}
	return jobs, nil
}

func (m *MockJobRepository) UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error {
	if m.shouldError {
		return apperrors.NewDatabaseError("mock update job description", nil)
	}
	job, exists := m.jobs[jobID]
	if !exists || job.UserID != userID {
		return apperrors.NewJobNotFoundError(jobID, userID)
	}
	job.Description = description
	return nil
}

func (m *MockJobRepository) DeleteJob(ctx context.Context, userID int, jobID string) error {
	if m.shouldError {
		return apperrors.NewDatabaseError("mock delete job", nil)
	}
	job, exists := m.jobs[jobID]
	if !exists || job.UserID != userID {
		return apperrors.NewJobNotFoundError(jobID, userID)
	}
	delete(m.jobs, jobID)
	return nil
}

// MockRepositoryFactory implements RepositoryFactory for testing
type MockRepositoryFactory struct {
	userRepo UserRepository
	jobRepo  JobRepository
}

// NewMockRepositoryFactory creates a new mock repository factory
func NewMockRepositoryFactory() *MockRepositoryFactory {
	return &MockRepositoryFactory{
		userRepo: NewMockUserRepository(),
		jobRepo:  NewMockJobRepository(),
	}
}

func (f *MockRepositoryFactory) UserRepository() UserRepository {
	return f.userRepo
}

func (f *MockRepositoryFactory) JobRepository() JobRepository {
	return f.jobRepo
}

func (f *MockRepositoryFactory) Close(ctx context.Context) error {
	return nil
}
