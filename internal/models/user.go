package models

import (
	"time"
)

type User struct {
	ID        int       `json:"id" bson:"_id"`
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`

	// OAuth provider associations
	GitHubOAuth   *GitHubOAuth   `json:"githubOauth,omitempty" bson:"githubOauth,omitempty"`
	GoogleOAuth   *GoogleOAuth   `json:"googleOauth,omitempty" bson:"googleOauth,omitempty"`
	LinkedInOAuth *LinkedInOAuth `json:"linkedinOauth,omitempty" bson:"linkedinOauth,omitempty"`
}

type GitHubOAuth struct {
	ID           int       `json:"id" bson:"id,omitempty"`
	UserID       int       `json:"userId" bson:"userId"`
	GitHubID     string    `json:"githubId" bson:"githubId"`
	Username     string    `json:"username" bson:"username"`
	AccessToken  string    `json:"-" bson:"accessToken"`
	RefreshToken *string   `json:"-" bson:"refreshToken,omitempty"`
	Email        *string   `json:"email,omitempty" bson:"email,omitempty"`
	AvatarURL    *string   `json:"avatarUrl,omitempty" bson:"avatarUrl,omitempty"`
	CreatedAt    time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt" bson:"updatedAt"`
}

type GoogleOAuth struct {
	ID           int       `json:"id" bson:"id,omitempty"`
	UserID       int       `json:"userId" bson:"userId"`
	GoogleID     string    `json:"googleId" bson:"googleId"`
	Email        string    `json:"email" bson:"email"`
	AccessToken  string    `json:"-" bson:"accessToken"`
	RefreshToken *string   `json:"-" bson:"refreshToken,omitempty"`
	AvatarURL    *string   `json:"avatarUrl,omitempty" bson:"avatarUrl,omitempty"`
	CreatedAt    time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt" bson:"updatedAt"`
}

type LinkedInOAuth struct {
	ID           int       `json:"id" bson:"id,omitempty"`
	UserID       int       `json:"userId" bson:"userId"`
	LinkedInID   string    `json:"linkedinId" bson:"linkedinId"`
	Email        string    `json:"email" bson:"email"`
	FirstName    *string   `json:"firstName,omitempty" bson:"firstName,omitempty"`
	LastName     *string   `json:"lastName,omitempty" bson:"lastName,omitempty"`
	AccessToken  string    `json:"-" bson:"accessToken"`
	RefreshToken *string   `json:"-" bson:"refreshToken,omitempty"`
	AvatarURL    *string   `json:"avatarUrl,omitempty" bson:"avatarUrl,omitempty"`
	CreatedAt    time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt" bson:"updatedAt"`
}
