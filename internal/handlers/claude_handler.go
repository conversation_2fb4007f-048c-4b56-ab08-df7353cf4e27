package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"github.com/eldon111/impact-resume-service/internal/services"
)

type <PERSON><PERSON><PERSON><PERSON> struct {
	claudeService *services.ClaudeService
	configService *services.ConfigService
}

type ClaudeRequest struct {
	Model  services.ClaudeModel `json:"model"`
	Prompt string               `json:"prompt"`
}

type ClaudeResponse struct {
	Response string               `json:"response"`
	Model    services.ClaudeModel `json:"model"`
	Usage    struct {
		InputTokens  int `json:"inputTokens"`
		OutputTokens int `json:"outputTokens"`
	} `json:"usage"`
}

func NewClaudeHandler(configService *services.ConfigService) (*<PERSON><PERSON><PERSON><PERSON>, error) {
	ctx := context.Background()
	apiKey, err := configService.GetClaudeAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get Claude API key: %w", err)
	}

	claudeService := services.NewClaudeService(apiKey)

	return &<PERSON><PERSON>ler{
		claudeService: claudeService,
		configService: configService,
	}, nil
}

// HandleClaudeRequest handles POST /api/v1/internal
// @Summary      Direct Claude API access
// @Description  Internal endpoint for direct access to Claude API
// @Tags         internal
// @Accept       json
// @Produce      json
// @Param        request  body      ClaudeRequest  true  "Claude request"
// @Success      200  {object}  ClaudeResponse
// @Failure      400  {object}  object{error=string}
// @Failure      405  {object}  object{error=string}
// @Failure      500  {object}  object{error=string}
// @Router       /internal [post]
func (ch *ClaudeHandler) HandleClaudeRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req ClaudeRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON request", http.StatusBadRequest)
		return
	}
	log.Println("Request body: ", req)

	if req.Prompt == "" {
		http.Error(w, "Prompt is required", http.StatusBadRequest)
		return
	}

	if req.Model == "" {
		req.Model = services.Claude3Haiku
	}

	model := req.Model
	messages := []services.Message{
		{
			Role:    "user",
			Content: req.Prompt,
		},
	}

	ctx := r.Context()
	response, err := ch.claudeService.CallClaude(ctx, model, messages, 4096)
	if err != nil {
		http.Error(w, fmt.Sprintf("Claude API error: %v", err), http.StatusInternalServerError)
		return
	}

	responseText := ""
	if len(response.Content) > 0 {
		responseText = response.Content[0].Text
	}

	claudeResp := ClaudeResponse{
		Response: responseText,
		Model:    services.ClaudeModel(response.Model),
		Usage: struct {
			InputTokens  int `json:"inputTokens"`
			OutputTokens int `json:"outputTokens"`
		}{
			InputTokens:  response.Usage.InputTokens,
			OutputTokens: response.Usage.OutputTokens,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(claudeResp); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}
