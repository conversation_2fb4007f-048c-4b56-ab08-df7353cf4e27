package handlers

import (
	"net/http"

	apperrors "github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
	"github.com/eldon111/impact-resume-service/internal/services"
	"github.com/labstack/echo/v4"
)

type JobHandler struct {
	jobService        services.JobServiceInterface
	processingService services.ProcessingServiceInterface
}

func NewJobHandler(jobService services.JobServiceInterface, processingService services.ProcessingServiceInterface) *JobHandler {
	return &JobHandler{
		jobService:        jobService,
		processingService: processingService,
	}
}

// RegisterEchoRoutes registers all job-related routes with the provided Echo group
func (jh *JobHandler) RegisterEchoRoutes(g *echo.Group) {
	// Job List Management (converted to Echo)
	g.GET("/jobs", jh.GetJobs)
	g.POST("/jobs", jh.CreateJob)

	// Job Description Management (converted to Echo)
	g.GET("/jobs/:id/description", jh.GetJobDescription)
	g.POST("/jobs/:id/description", jh.CreateJobDescription)
	g.PUT("/jobs/:id/description", jh.UpdateJobDescription)
	g.DELETE("/jobs/:id/description", jh.DeleteJobDescription)

	// Job Management (converted to Echo)
	g.DELETE("/jobs/:id", jh.DeleteJob)

	// Resume Processing (converted to Echo)
	g.POST("/jobs/:id/process", jh.ProcessJob)
	g.POST("/jobs/process", jh.ProcessMultipleJobs)
}

// Job Description Management Handlers

// GetJobDescription handles GET /api/v1/jobs/{id}/description
// @Summary      Get job description
// @Description  Retrieve the description for a specific job (user-specific)
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      string  true  "Job ID"
// @Success      200  {object}  object{id=string,description=string}
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Router       /jobs/{id}/description [get]
func (jh *JobHandler) GetJobDescription(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	jobID := c.Param("id")
	if jobID == "" {
		return apperrors.NewInvalidInputError("jobID", jobID).
			WithDetails("Job ID parameter is required")
	}

	job, err := jh.jobService.GetJob(c.Request().Context(), userID, jobID)
	if err != nil {
		return err // Service already returns proper error types, let middleware handle conversion
	}

	response := struct {
		ID          string `json:"id"`
		Description string `json:"description"`
	}{
		ID:          job.ID,
		Description: job.Description,
	}

	return c.JSON(http.StatusOK, response)
}

// CreateJobDescription handles POST /api/v1/jobs/{id}/description
// @Summary      Create job description
// @Description  Create or update the description for a specific job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Param        id   path      string  true  "Job ID"
// @Param        description  body      object{description=string}  true  "Job description"
// @Success      200  {object}  SuccessResponse
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Security     BearerAuth
// @Router       /jobs/{id}/description [post]
func (jh *JobHandler) CreateJobDescription(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	jobID := c.Param("id")
	if jobID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid job ID")
	}

	var req struct {
		Description string `json:"description" validate:"required,max=5000"`
	}
	if err := c.Bind(&req); err != nil {
		return apperrors.NewInvalidInputError("request", req).
			WithDetails("Invalid JSON request format")
	}

	err = jh.jobService.UpdateJobDescription(c.Request().Context(), userID, jobID, req.Description)
	if err != nil {
		return err // Service already returns proper error types, let middleware handle conversion
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Description updated successfully",
	})
}

// UpdateJobDescription handles PUT /api/v1/jobs/{id}/description
// @Summary      Update job description
// @Description  Update the description for a specific job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Param        id   path      string  true  "Job ID"
// @Param        description  body      object{description=string}  true  "Job description"
// @Success      200  {object}  SuccessResponse
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Security     BearerAuth
// @Router       /jobs/{id}/description [put]
func (jh *JobHandler) UpdateJobDescription(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	jobID := c.Param("id")
	if jobID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid job ID")
	}

	var req struct {
		Description string `json:"description" validate:"required,max=5000"`
	}
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid JSON request")
	}

	err = jh.jobService.UpdateJobDescription(c.Request().Context(), userID, jobID, req.Description)
	if err != nil {
		return err // Service already returns proper error types, let middleware handle conversion
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Description updated successfully",
	})
}

// DeleteJobDescription handles DELETE /api/v1/jobs/{id}/description
// @Summary      Delete job description
// @Description  Delete the description for a specific job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Param        id   path      string  true  "Job ID"
// @Success      200  {object}  SuccessResponse
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Security     BearerAuth
// @Router       /jobs/{id}/description [delete]
func (jh *JobHandler) DeleteJobDescription(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	jobID := c.Param("id")
	if jobID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid job ID")
	}

	err = jh.jobService.UpdateJobDescription(c.Request().Context(), userID, jobID, "")
	if err != nil {
		return err // Service already returns proper error types, let middleware handle conversion
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Description deleted successfully",
	})
}

// Job List Management Handlers

// GetJobs handles GET /api/v1/jobs
// @Summary      Get all jobs
// @Description  Retrieve all jobs for the current user
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Success      200  {object}  models.JobListResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Security     BearerAuth
// @Router       /jobs [get]
func (jh *JobHandler) GetJobs(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	jobs, err := jh.jobService.GetJobs(c.Request().Context(), userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to retrieve jobs")
	}

	response := models.JobListResponse{
		Jobs:  jobs,
		Total: len(jobs),
	}

	return c.JSON(http.StatusOK, response)
}

// CreateJob handles POST /api/v1/jobs
// @Summary      Create a new job
// @Description  Create a new job entry
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Param        job  body      models.JobCreateRequest  true  "Job data"
// @Success      201  {object}  models.Job
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Security     BearerAuth
// @Router       /jobs [post]
func (jh *JobHandler) CreateJob(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	var req models.JobCreateRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid JSON request")
	}

	// Create job using service
	job, err := jh.jobService.CreateJob(c.Request().Context(), userID, &req)
	if err != nil {
		return err // Service already returns proper error types, let middleware handle conversion
	}

	return c.JSON(http.StatusCreated, job)
}

// DeleteJob handles DELETE /api/v1/jobs/{id}
// @Summary      Delete job
// @Description  Delete a specific job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Param        id   path      string  true  "Job ID"
// @Success      200  {object}  SuccessResponse
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Security     BearerAuth
// @Router       /jobs/{id} [delete]
func (jh *JobHandler) DeleteJob(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	jobID := c.Param("id")
	if jobID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid job ID")
	}

	err = jh.jobService.DeleteJob(c.Request().Context(), userID, jobID)
	if err != nil {
		return err // Service already returns proper error types, let middleware handle conversion
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Job deleted successfully",
	})
}

// Resume Processing Handlers

// ProcessJob handles POST /api/v1/jobs/{id}/process
// @Summary      Process a job with AI
// @Description  Process a job description with Claude AI to generate bullet points
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Param        id   path      string  true  "Job ID"
// @Success      200  {object}  models.ProcessedJob
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Security     BearerAuth
// @Router       /jobs/{id}/process [post]
func (jh *JobHandler) ProcessJob(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	jobID := c.Param("id")
	if jobID == "" {
		return apperrors.NewInvalidInputError("jobID", jobID).
			WithDetails("Job ID parameter is required")
	}

	// Process job using service
	processedJob, err := jh.processingService.ProcessSingleJob(c.Request().Context(), userID, jobID)
	if err != nil {
		return err // Service already returns proper error types, let middleware handle conversion
	}

	return c.JSON(http.StatusOK, processedJob)
}

// ProcessMultipleJobs handles POST /api/v1/jobs/process
// @Summary      Process multiple jobs with AI
// @Description  Process multiple job descriptions with Claude AI to generate bullet points
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Param        jobs  body      models.JobProcessRequest  true  "Job IDs to process"
// @Success      200  {object}  object{processedJobs=[]models.ProcessedJob,total=int}
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Failure      404  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Security     BearerAuth
// @Router       /jobs/process [post]
func (jh *JobHandler) ProcessMultipleJobs(c echo.Context) error {
	userID, err := GetUserID(c)
	if err != nil {
		return err
	}

	var req models.JobProcessRequest
	if err := c.Bind(&req); err != nil {
		return apperrors.NewInvalidInputError("request", req).
			WithDetails("Invalid JSON request format")
	}

	// Process multiple jobs using service
	processedJobs, err := jh.processingService.ProcessMultipleJobs(c.Request().Context(), userID, req.JobIDs)
	if err != nil {
		return err // Service already returns proper error types, let middleware handle conversion
	}

	response := struct {
		ProcessedJobs []models.ProcessedJob `json:"processedJobs"`
		Total         int                   `json:"total"`
	}{
		ProcessedJobs: processedJobs,
		Total:         len(processedJobs),
	}

	return c.JSON(http.StatusOK, response)
}
