#!/bin/bash

# MongoDB Database Initialization Script
# This script sets up the impact-resume-service database with collections and indexes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Impact Resume Service Database Initialization${NC}"
echo "========================================"

# Check if MongoDB is running
if ! pgrep -x "mongod" > /dev/null; then
    echo -e "${RED}❌ MongoDB is not running. Please start MongoDB first:${NC}"
    echo "   sudo systemctl start mongodb"
    exit 1
fi

# Check if mongosh is available
if ! command -v mongosh &> /dev/null; then
    echo -e "${RED}❌ mongosh is not installed. Please install MongoDB shell.${NC}"
    exit 1
fi

# Get database name from environment or use default
DB_NAME=${MONGODB_DATABASE:-impact-resume-service}
echo -e "${YELLOW}📊 Database: $DB_NAME${NC}"

# Run the initialization script
echo -e "${YELLOW}🔧 Running database initialization...${NC}"
mongosh "$DB_NAME" "$(dirname "$0")/init-db.js"

echo -e "${GREEN}✅ Database initialization completed successfully!${NC}"
echo ""
echo "Next steps:"
echo "1. Set environment variables (copy .env.example to .env if needed)"
echo "2. Start your application: go run ./cmd/impactresume"
echo "3. Test the connection at http://localhost:8080"